from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime

from models.database import get_db, User, Video, VideoShare
from models.auth_schemas import (
    VideoShareCreate, VideoShareResponse, VideoShareUpdate,
    SharedVideoResponse, BulkShareRequest, BulkShareResponse
)
from utils.auth import get_current_active_user, check_video_access

router = APIRouter()

@router.post("/videos/{video_id}/share", response_model=VideoShareResponse)
async def share_video(
    video_id: int,
    share_data: VideoShareCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Share a video with another user"""
    # Get the video and check ownership
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    if video.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only share your own videos"
        )
    
    # Find the user to share with
    shared_with_user = db.query(User).filter(User.email == share_data.shared_with_email).first()
    if not shared_with_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    if shared_with_user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot share video with yourself"
        )
    
    # Check if already shared
    existing_share = db.query(VideoShare).filter(
        VideoShare.video_id == video_id,
        VideoShare.shared_with_user_id == shared_with_user.id
    ).first()
    
    if existing_share:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Video already shared with this user"
        )
    
    # Create the share
    video_share = VideoShare(
        video_id=video_id,
        shared_with_user_id=shared_with_user.id,
        shared_by_user_id=current_user.id,
        permissions=share_data.permissions,
        expires_at=share_data.expires_at
    )
    
    db.add(video_share)
    db.commit()
    db.refresh(video_share)
    
    return video_share

@router.get("/videos/{video_id}/shares", response_model=List[VideoShareResponse])
async def get_video_shares(
    video_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all shares for a video"""
    # Get the video and check ownership
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    if video.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only view shares for your own videos"
        )
    
    shares = db.query(VideoShare).filter(VideoShare.video_id == video_id).all()
    return shares

@router.put("/shares/{share_id}", response_model=VideoShareResponse)
async def update_video_share(
    share_id: int,
    share_update: VideoShareUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a video share"""
    share = db.query(VideoShare).filter(VideoShare.id == share_id).first()
    if not share:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Share not found"
        )
    
    # Check if user owns the video
    video = db.query(Video).filter(Video.id == share.video_id).first()
    if video.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update shares for your own videos"
        )
    
    # Update the share
    if share_update.permissions is not None:
        share.permissions = share_update.permissions
    if share_update.expires_at is not None:
        share.expires_at = share_update.expires_at
    
    db.commit()
    db.refresh(share)
    
    return share

@router.delete("/shares/{share_id}")
async def delete_video_share(
    share_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Remove a video share"""
    share = db.query(VideoShare).filter(VideoShare.id == share_id).first()
    if not share:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Share not found"
        )
    
    # Check if user owns the video or is the recipient
    video = db.query(Video).filter(Video.id == share.video_id).first()
    if video.user_id != current_user.id and share.shared_with_user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only remove shares for your own videos or shares with you"
        )
    
    db.delete(share)
    db.commit()
    
    return {"message": "Share removed successfully"}

@router.get("/shared-with-me", response_model=List[SharedVideoResponse])
async def get_shared_videos(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get videos shared with the current user"""
    shares = db.query(VideoShare).filter(
        VideoShare.shared_with_user_id == current_user.id
    ).join(Video).all()
    
    # Filter out expired shares
    active_shares = []
    for share in shares:
        if share.expires_at is None or share.expires_at > datetime.utcnow():
            active_shares.append(share)
    
    return active_shares

@router.post("/videos/bulk-share", response_model=BulkShareResponse)
async def bulk_share_videos(
    bulk_share: BulkShareRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Share multiple videos with multiple users"""
    result = {
        "success": True,
        "message": "",
        "processed_count": 0,
        "failed_count": 0,
        "failed_items": []
    }
    
    # Validate videos belong to user
    videos = db.query(Video).filter(
        Video.id.in_(bulk_share.video_ids),
        Video.user_id == current_user.id
    ).all()
    
    if len(videos) != len(bulk_share.video_ids):
        result["success"] = False
        result["message"] = "Some videos not found or not owned by user"
        return result
    
    # Find users to share with
    users_to_share_with = db.query(User).filter(
        User.email.in_(bulk_share.shared_with_emails),
        User.is_active == True
    ).all()
    
    user_emails = {user.email: user for user in users_to_share_with}
    
    # Process each combination
    for video in videos:
        for email in bulk_share.shared_with_emails:
            try:
                if email not in user_emails:
                    result["failed_count"] += 1
                    result["failed_items"].append({
                        "video_id": video.id,
                        "email": email,
                        "error": "User not found"
                    })
                    continue
                
                shared_with_user = user_emails[email]
                
                if shared_with_user.id == current_user.id:
                    result["failed_count"] += 1
                    result["failed_items"].append({
                        "video_id": video.id,
                        "email": email,
                        "error": "Cannot share with yourself"
                    })
                    continue
                
                # Check if already shared
                existing_share = db.query(VideoShare).filter(
                    VideoShare.video_id == video.id,
                    VideoShare.shared_with_user_id == shared_with_user.id
                ).first()
                
                if existing_share:
                    result["failed_count"] += 1
                    result["failed_items"].append({
                        "video_id": video.id,
                        "email": email,
                        "error": "Already shared"
                    })
                    continue
                
                # Create the share
                video_share = VideoShare(
                    video_id=video.id,
                    shared_with_user_id=shared_with_user.id,
                    shared_by_user_id=current_user.id,
                    permissions=bulk_share.permissions,
                    expires_at=bulk_share.expires_at
                )
                
                db.add(video_share)
                result["processed_count"] += 1
                
            except Exception as e:
                result["failed_count"] += 1
                result["failed_items"].append({
                    "video_id": video.id,
                    "email": email,
                    "error": str(e)
                })
    
    db.commit()
    
    if result["failed_count"] > 0:
        result["success"] = False
        result["message"] = f"Processed {result['processed_count']} shares, {result['failed_count']} failed"
    else:
        result["message"] = f"Successfully shared {result['processed_count']} video-user combinations"
    
    return result
