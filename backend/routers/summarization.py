from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import shutil
import uuid
from datetime import datetime

from models.database import get_db, Video, Meeting<PERSON><PERSON><PERSON>y, Speaker, ActionItem
from models.schemas import (
    VideoResponse, SummarizationRequest, SummarizationResponse, SummarizationStatus,
    MeetingSummaryResponse, SpeakerResponse, ActionItemResponse, UploadResponse,
    MeetingSummaryCreate, MeetingSummaryUpdate, SpeakerCreate, SpeakerUpdate,
    ActionItemCreate, ActionItemUpdate, PaginationParams
)
from services.video_service import VideoService
from services.summarization_service import SummarizationService

router = APIRouter()

@router.post("/upload", response_model=UploadResponse)
async def upload_meeting_videos(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db)
):
    """Upload multiple meeting video files for summarization"""
    video_service = VideoService(db)
    summarization_service = SummarizationService(db)
    
    uploaded_files = []
    failed_files = []
    
    for file in files:
        try:
            # Validate file type
            if not file.content_type or not file.content_type.startswith('video/'):
                failed_files.append({
                    "filename": file.filename,
                    "error": "Invalid file type. Only video files are allowed."
                })
                continue
            
            # Generate unique filename
            file_extension = os.path.splitext(file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_path = os.path.join("videos", unique_filename)
            
            # Save file
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # Create video record with meeting flag
            video_data = {
                "filename": unique_filename,
                "original_filename": file.filename,
                "file_path": file_path,
                "file_size": os.path.getsize(file_path),
                "is_meeting": True  # Mark as meeting video
            }
            
            video = video_service.create_video(video_data)
            uploaded_files.append(file.filename)
            
            # Queue for processing (transcription + speaker diarization + summarization)
            background_tasks.add_task(
                summarization_service.process_meeting_video_async,
                video.id
            )
            
        except Exception as e:
            failed_files.append({
                "filename": file.filename,
                "error": str(e)
            })
            # Clean up file if it was created
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
    
    return UploadResponse(
        message=f"Uploaded {len(uploaded_files)} files successfully",
        uploaded_files=uploaded_files,
        failed_files=failed_files,
        total_uploaded=len(uploaded_files)
    )

@router.get("/videos", response_model=List[VideoResponse])
async def get_meeting_videos(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    status: Optional[str] = Query(None, description="Filter by summary status"),
    db: Session = Depends(get_db)
):
    """Get list of meeting videos with optional filtering"""
    video_service = VideoService(db)
    
    # Get only meeting videos
    videos = video_service.get_meeting_videos(
        skip=skip,
        limit=limit,
        summary_status=status
    )
    
    return videos

@router.get("/videos/{video_id}", response_model=VideoResponse)
async def get_meeting_video(video_id: int, db: Session = Depends(get_db)):
    """Get a specific meeting video with summary details"""
    video_service = VideoService(db)
    video = video_service.get_video_by_id(video_id)
    
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    if not video.is_meeting:
        raise HTTPException(status_code=400, detail="Video is not marked as a meeting")
    
    return video

@router.post("/videos/{video_id}/summarize", response_model=SummarizationResponse)
async def start_summarization(
    video_id: int,
    request: SummarizationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Start or restart summarization process for a video"""
    summarization_service = SummarizationService(db)
    
    # Validate video exists and is a meeting
    video = summarization_service.get_video_by_id(video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    if not video.is_meeting:
        raise HTTPException(status_code=400, detail="Video is not marked as a meeting")
    
    # Start summarization process
    job_id = await summarization_service.start_summarization(
        video_id=video_id,
        enable_speaker_diarization=request.enable_speaker_diarization,
        background_tasks=background_tasks
    )
    
    return SummarizationResponse(
        message="Summarization process started",
        video_id=video_id,
        job_id=job_id,
        estimated_completion_time="5-10 minutes"
    )

@router.get("/videos/{video_id}/status", response_model=SummarizationStatus)
async def get_summarization_status(video_id: int, db: Session = Depends(get_db)):
    """Get summarization status for a video"""
    summarization_service = SummarizationService(db)
    
    video = summarization_service.get_video_by_id(video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    return SummarizationStatus(
        video_id=video_id,
        status=video.summary_status,
        progress=video.summary_progress,
        error_message=None,  # TODO: Add error tracking
        estimated_completion_time="5-10 minutes" if video.summary_status == "processing" else None
    )

@router.get("/videos/{video_id}/summary", response_model=MeetingSummaryResponse)
async def get_meeting_summary(video_id: int, db: Session = Depends(get_db)):
    """Get the meeting summary for a video"""
    summarization_service = SummarizationService(db)
    
    summary = summarization_service.get_meeting_summary(video_id)
    if not summary:
        raise HTTPException(status_code=404, detail="Meeting summary not found")
    
    return summary

@router.put("/videos/{video_id}/summary", response_model=MeetingSummaryResponse)
async def update_meeting_summary(
    video_id: int,
    summary_update: MeetingSummaryUpdate,
    db: Session = Depends(get_db)
):
    """Update meeting summary"""
    summarization_service = SummarizationService(db)
    
    summary = summarization_service.update_meeting_summary(video_id, summary_update)
    if not summary:
        raise HTTPException(status_code=404, detail="Meeting summary not found")
    
    return summary

@router.get("/videos/{video_id}/speakers", response_model=List[SpeakerResponse])
async def get_speakers(video_id: int, db: Session = Depends(get_db)):
    """Get speakers for a video"""
    summarization_service = SummarizationService(db)
    
    speakers = summarization_service.get_speakers(video_id)
    return speakers

@router.put("/speakers/{speaker_id}", response_model=SpeakerResponse)
async def update_speaker(
    speaker_id: int,
    speaker_update: SpeakerUpdate,
    db: Session = Depends(get_db)
):
    """Update speaker information"""
    summarization_service = SummarizationService(db)
    
    speaker = summarization_service.update_speaker(speaker_id, speaker_update)
    if not speaker:
        raise HTTPException(status_code=404, detail="Speaker not found")
    
    return speaker

@router.get("/videos/{video_id}/action-items", response_model=List[ActionItemResponse])
async def get_action_items(video_id: int, db: Session = Depends(get_db)):
    """Get action items for a meeting"""
    summarization_service = SummarizationService(db)
    
    action_items = summarization_service.get_action_items(video_id)
    return action_items

@router.post("/videos/{video_id}/action-items", response_model=ActionItemResponse)
async def create_action_item(
    video_id: int,
    action_item: ActionItemCreate,
    db: Session = Depends(get_db)
):
    """Create a new action item"""
    summarization_service = SummarizationService(db)
    
    # Validate that meeting summary exists
    summary = summarization_service.get_meeting_summary(video_id)
    if not summary:
        raise HTTPException(status_code=404, detail="Meeting summary not found")
    
    action_item.meeting_summary_id = summary.id
    new_action_item = summarization_service.create_action_item(action_item)
    
    return new_action_item

@router.put("/action-items/{action_item_id}", response_model=ActionItemResponse)
async def update_action_item(
    action_item_id: int,
    action_item_update: ActionItemUpdate,
    db: Session = Depends(get_db)
):
    """Update an action item"""
    summarization_service = SummarizationService(db)
    
    action_item = summarization_service.update_action_item(action_item_id, action_item_update)
    if not action_item:
        raise HTTPException(status_code=404, detail="Action item not found")
    
    return action_item

@router.delete("/action-items/{action_item_id}")
async def delete_action_item(action_item_id: int, db: Session = Depends(get_db)):
    """Delete an action item"""
    summarization_service = SummarizationService(db)
    
    success = summarization_service.delete_action_item(action_item_id)
    if not success:
        raise HTTPException(status_code=404, detail="Action item not found")
    
    return {"message": "Action item deleted successfully"}

@router.delete("/videos/{video_id}")
async def delete_meeting_video(video_id: int, db: Session = Depends(get_db)):
    """Delete a meeting video and all associated data"""
    summarization_service = SummarizationService(db)
    
    success = summarization_service.delete_meeting_video(video_id)
    if not success:
        raise HTTPException(status_code=404, detail="Video not found")
    
    return {"message": "Meeting video and associated data deleted successfully"}
