import asyncio
import os
import j<PERSON>
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime

from models.database import Video, MeetingS<PERSON>mary, Speaker, ActionItem, ProcessingJob
from models.schemas import (
    MeetingSummaryCreate, MeetingSummaryUpdate, SpeakerCreate, SpeakerUpdate,
    ActionItemCreate, ActionItemUpdate, MeetingSummaryResponse, SpeakerResponse,
    ActionItemResponse
)
from services.video_service import VideoService
from utils.ai_utils import Whisper<PERSON>ranscriber, SpeakerDiarizer, MeetingSummarizer

class SummarizationService:
    def __init__(self, db: Session):
        self.db = db
        self.video_service = VideoService(db)
        self.transcriber = WhisperTranscriber()
        self.diarizer = SpeakerDiarizer()
        self.summarizer = MeetingSummarizer()
    
    async def process_meeting_video_async(self, video_id: int):
        """Complete processing pipeline for meeting videos"""
        try:
            # Update status to processing
            self.update_summary_status(video_id, "processing", progress=0)
            
            # Create processing job
            job = ProcessingJob(
                video_id=video_id,
                job_type="summarization",
                status="running",
                started_at=datetime.now()
            )
            self.db.add(job)
            self.db.commit()
            
            # Get video
            video = self.get_video_by_id(video_id)
            if not video:
                raise Exception(f"Video {video_id} not found")
            
            # Step 1: Transcribe video (0% -> 30%)
            self.update_summary_status(video_id, "processing", progress=10)
            print(f"Starting transcription for meeting video {video_id}")
            
            transcript, language = await self.transcriber.transcribe(video.file_path)
            
            # Update video with basic transcript
            video.transcript = transcript
            video.transcript_language = language
            self.db.commit()
            
            self.update_summary_status(video_id, "processing", progress=30)
            print(f"Transcription completed for meeting video {video_id}")
            
            # Step 2: Speaker diarization (30% -> 60%)
            self.update_summary_status(video_id, "processing", progress=35)
            print(f"Starting speaker diarization for video {video_id}")
            
            speaker_transcript, speakers_info = await self.diarizer.diarize_audio(
                video.file_path, transcript
            )
            
            # Update video with speaker transcript
            video.speaker_transcript = speaker_transcript
            self.db.commit()
            
            # Create speaker records
            await self.create_speakers_from_diarization(video_id, speakers_info)
            
            self.update_summary_status(video_id, "processing", progress=60)
            print(f"Speaker diarization completed for video {video_id}")
            
            # Step 3: Generate meeting summary (60% -> 90%)
            self.update_summary_status(video_id, "processing", progress=65)
            print(f"Starting meeting summarization for video {video_id}")
            
            summary_data = await self.summarizer.generate_meeting_summary(
                speaker_transcript, video.title or video.original_filename
            )
            
            # Create meeting summary
            await self.create_meeting_summary_from_ai(video_id, summary_data)
            
            self.update_summary_status(video_id, "processing", progress=90)
            print(f"Meeting summarization completed for video {video_id}")
            
            # Step 4: Generate tags (90% -> 100%)
            self.update_summary_status(video_id, "processing", progress=95)
            print(f"Generating tags for meeting video {video_id}")
            
            # Use existing tag generation from processing service
            from services.processing_service import ProcessingService
            processing_service = ProcessingService(self.db)
            
            suggested_tags = await processing_service.generate_tags_from_transcript(
                speaker_transcript, video.title or video.original_filename
            )
            
            # Create and assign tags
            for tag_data in suggested_tags:
                # Check if tag exists
                from services.tag_service import TagService
                tag_service = TagService(self.db)
                existing_tag = tag_service.get_tag_by_name(tag_data["name"])
                if not existing_tag:
                    # Create new tag
                    from models.schemas import TagCreate
                    tag_create = TagCreate(
                        name=tag_data["name"],
                        color=tag_data["color"],
                        description=tag_data["description"]
                    )
                    tag = tag_service.create_tag(tag_create)
                else:
                    tag = existing_tag
                
                # Add tag to video
                tag_service.add_tag_to_video(tag.id, video_id)
            
            # Mark as completed
            self.update_summary_status(video_id, "completed", progress=100)
            video.processed = True
            video.processing_status = "completed"
            video.processing_progress = 100
            
            # Update job status
            job.status = "completed"
            job.completed_at = datetime.now()
            self.db.commit()
            
            print(f"Meeting video processing completed for video {video_id}")
            
        except Exception as e:
            print(f"Meeting video processing failed for video {video_id}: {e}")
            self.update_summary_status(video_id, "failed", progress=0)
            
            # Update job status
            if 'job' in locals():
                job.status = "failed"
                job.error_message = str(e)
                job.completed_at = datetime.now()
                self.db.commit()
    
    async def start_summarization(self, video_id: int, enable_speaker_diarization: bool = True, background_tasks=None):
        """Start summarization process for an existing video"""
        # Reset summary status
        self.update_summary_status(video_id, "processing", progress=0)
        
        # Create processing job
        job = ProcessingJob(
            video_id=video_id,
            job_type="summarization",
            status="running",
            started_at=datetime.now()
        )
        self.db.add(job)
        self.db.commit()
        
        # Queue for processing
        if background_tasks:
            background_tasks.add_task(self.process_meeting_video_async, video_id)
        else:
            asyncio.create_task(self.process_meeting_video_async(video_id))
        
        return job.id
    
    def get_video_by_id(self, video_id: int) -> Optional[Video]:
        """Get video by ID"""
        return self.db.query(Video).filter(Video.id == video_id).first()
    
    def get_meeting_videos(self, skip: int = 0, limit: int = 50, summary_status: Optional[str] = None) -> List[Video]:
        """Get meeting videos with optional filtering"""
        query = self.db.query(Video).filter(Video.is_meeting == True)
        
        if summary_status:
            query = query.filter(Video.summary_status == summary_status)
        
        return query.offset(skip).limit(limit).all()
    
    def update_summary_status(self, video_id: int, status: str, progress: int = 0):
        """Update summary status for a video"""
        video = self.get_video_by_id(video_id)
        if video:
            video.summary_status = status
            video.summary_progress = progress
            self.db.commit()
    
    def get_meeting_summary(self, video_id: int) -> Optional[MeetingSummary]:
        """Get meeting summary for a video"""
        return self.db.query(MeetingSummary).filter(MeetingSummary.video_id == video_id).first()
    
    def create_meeting_summary(self, summary_data: MeetingSummaryCreate) -> MeetingSummary:
        """Create a new meeting summary"""
        summary = MeetingSummary(**summary_data.dict())
        self.db.add(summary)
        self.db.commit()
        self.db.refresh(summary)
        return summary
    
    def update_meeting_summary(self, video_id: int, summary_update: MeetingSummaryUpdate) -> Optional[MeetingSummary]:
        """Update meeting summary"""
        summary = self.get_meeting_summary(video_id)
        if not summary:
            return None
        
        update_data = summary_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(summary, field, value)
        
        summary.updated_date = datetime.now()
        self.db.commit()
        self.db.refresh(summary)
        return summary
    
    def get_speakers(self, video_id: int) -> List[Speaker]:
        """Get speakers for a video"""
        return self.db.query(Speaker).filter(Speaker.video_id == video_id).all()
    
    def create_speaker(self, speaker_data: SpeakerCreate) -> Speaker:
        """Create a new speaker"""
        speaker = Speaker(**speaker_data.dict())
        self.db.add(speaker)
        self.db.commit()
        self.db.refresh(speaker)
        return speaker
    
    def update_speaker(self, speaker_id: int, speaker_update: SpeakerUpdate) -> Optional[Speaker]:
        """Update speaker information"""
        speaker = self.db.query(Speaker).filter(Speaker.id == speaker_id).first()
        if not speaker:
            return None
        
        update_data = speaker_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(speaker, field, value)
        
        self.db.commit()
        self.db.refresh(speaker)
        return speaker
    
    def get_action_items(self, video_id: int) -> List[ActionItem]:
        """Get action items for a meeting"""
        summary = self.get_meeting_summary(video_id)
        if not summary:
            return []
        
        return self.db.query(ActionItem).filter(ActionItem.meeting_summary_id == summary.id).all()
    
    def create_action_item(self, action_item_data: ActionItemCreate) -> ActionItem:
        """Create a new action item"""
        action_item = ActionItem(**action_item_data.dict())
        self.db.add(action_item)
        self.db.commit()
        self.db.refresh(action_item)
        return action_item
    
    def update_action_item(self, action_item_id: int, action_item_update: ActionItemUpdate) -> Optional[ActionItem]:
        """Update an action item"""
        action_item = self.db.query(ActionItem).filter(ActionItem.id == action_item_id).first()
        if not action_item:
            return None
        
        update_data = action_item_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(action_item, field, value)
        
        action_item.updated_date = datetime.now()
        self.db.commit()
        self.db.refresh(action_item)
        return action_item
    
    def delete_action_item(self, action_item_id: int) -> bool:
        """Delete an action item"""
        action_item = self.db.query(ActionItem).filter(ActionItem.id == action_item_id).first()
        if not action_item:
            return False
        
        self.db.delete(action_item)
        self.db.commit()
        return True
    
    def delete_meeting_video(self, video_id: int) -> bool:
        """Delete a meeting video and all associated data"""
        video = self.get_video_by_id(video_id)
        if not video:
            return False
        
        # Delete associated files
        if os.path.exists(video.file_path):
            os.remove(video.file_path)
        
        if video.thumbnail_path and os.path.exists(video.thumbnail_path):
            os.remove(video.thumbnail_path)
        
        # Database cascade will handle related records
        self.db.delete(video)
        self.db.commit()
        return True
    
    async def create_speakers_from_diarization(self, video_id: int, speakers_info: List[Dict[str, Any]]):
        """Create speaker records from diarization results"""
        for speaker_info in speakers_info:
            speaker_data = SpeakerCreate(
                video_id=video_id,
                speaker_label=speaker_info["label"],
                total_speaking_time=speaker_info.get("total_time", 0.0)
            )
            self.create_speaker(speaker_data)
    
    async def create_meeting_summary_from_ai(self, video_id: int, summary_data: Dict[str, Any]):
        """Create meeting summary from AI-generated data"""
        # Create the main summary
        summary_create = MeetingSummaryCreate(
            video_id=video_id,
            overview=summary_data["overview"],
            key_points=json.dumps(summary_data.get("key_points", []))
        )
        summary = self.create_meeting_summary(summary_create)
        
        # Create action items
        for action_data in summary_data.get("action_items", []):
            # Find assigned speaker if specified
            assigned_speaker_id = None
            if action_data.get("assigned_to"):
                speakers = self.get_speakers(video_id)
                for speaker in speakers:
                    if speaker.speaker_label.lower() in action_data["assigned_to"].lower():
                        assigned_speaker_id = speaker.id
                        break
            
            action_item_create = ActionItemCreate(
                meeting_summary_id=summary.id,
                assigned_speaker_id=assigned_speaker_id,
                description=action_data["description"],
                priority=action_data.get("priority", "medium")
            )
            self.create_action_item(action_item_create)
