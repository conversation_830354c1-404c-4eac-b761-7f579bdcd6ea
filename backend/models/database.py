from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Float, Boolean, ForeignKey, Table
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
import os

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///db/tagTok.db")

# Create engine
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {}
)

# Create session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# Association table for many-to-many relationship between videos and tags
video_tags = Table(
    'video_tags',
    Base.metadata,
    Column('video_id', Integer, ForeignKey('videos.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True)
)

class Video(Base):
    __tablename__ = "videos"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String, unique=True, index=True, nullable=False)
    original_filename = Column(String, nullable=False)
    title = Column(String, nullable=True)
    file_path = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)
    duration = Column(Float, nullable=True)
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    fps = Column(Float, nullable=True)
    thumbnail_path = Column(String, nullable=True)
    transcript = Column(Text, nullable=True)
    transcript_language = Column(String, nullable=True)
    upload_date = Column(DateTime(timezone=True), server_default=func.now())
    processed = Column(Boolean, default=False)
    processing_status = Column(String, default="pending")  # pending, processing, completed, failed
    processing_progress = Column(Integer, default=0)  # 0-100 percentage

    # Download-related fields
    source_url = Column(String, nullable=True)  # URL if downloaded from web
    download_status = Column(String, nullable=True)  # downloading, completed, failed
    download_error = Column(Text, nullable=True)  # Error message if download failed
    download_progress = Column(Integer, default=0)  # 0-100 percentage for downloads

    # Summarization-related fields
    is_meeting = Column(Boolean, default=False)  # Whether this video is a meeting recording
    speaker_transcript = Column(Text, nullable=True)  # Transcript with speaker identification
    summary_status = Column(String, default="none")  # none, processing, completed, failed
    summary_progress = Column(Integer, default=0)  # 0-100 percentage for summary generation

    # Relationships
    tags = relationship("Tag", secondary=video_tags, back_populates="videos")
    meeting_summary = relationship("MeetingSummary", back_populates="video", uselist=False)
    speakers = relationship("Speaker", back_populates="video")

class Tag(Base):
    __tablename__ = "tags"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)
    color = Column(String, nullable=False)  # Hex color code
    description = Column(Text, nullable=True)
    created_date = Column(DateTime(timezone=True), server_default=func.now())
    usage_count = Column(Integer, default=0)
    
    # Relationships
    videos = relationship("Video", secondary=video_tags, back_populates="tags")

class ProcessingJob(Base):
    __tablename__ = "processing_jobs"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    job_type = Column(String, nullable=False)  # transcription, tagging, thumbnail, summarization
    status = Column(String, default="pending")  # pending, running, completed, failed
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)

    # Relationship
    video = relationship("Video")

class MeetingSummary(Base):
    __tablename__ = "meeting_summaries"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False, unique=True)
    overview = Column(Text, nullable=False)  # Brief overview of the meeting
    key_points = Column(Text, nullable=True)  # JSON array of key discussion points
    created_date = Column(DateTime(timezone=True), server_default=func.now())
    updated_date = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    video = relationship("Video", back_populates="meeting_summary")
    action_items = relationship("ActionItem", back_populates="meeting_summary", cascade="all, delete-orphan")

class Speaker(Base):
    __tablename__ = "speakers"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    speaker_label = Column(String, nullable=False)  # e.g., "Speaker 1", "Speaker 2"
    display_name = Column(String, nullable=True)  # Optional custom name
    total_speaking_time = Column(Float, nullable=True)  # Total seconds spoken
    created_date = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    video = relationship("Video", back_populates="speakers")
    action_items = relationship("ActionItem", back_populates="assigned_speaker")

class ActionItem(Base):
    __tablename__ = "action_items"

    id = Column(Integer, primary_key=True, index=True)
    meeting_summary_id = Column(Integer, ForeignKey("meeting_summaries.id"), nullable=False)
    assigned_speaker_id = Column(Integer, ForeignKey("speakers.id"), nullable=True)
    description = Column(Text, nullable=False)
    priority = Column(String, default="medium")  # low, medium, high
    status = Column(String, default="pending")  # pending, in_progress, completed
    due_date = Column(DateTime(timezone=True), nullable=True)
    created_date = Column(DateTime(timezone=True), server_default=func.now())
    updated_date = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    meeting_summary = relationship("MeetingSummary", back_populates="action_items")
    assigned_speaker = relationship("Speaker", back_populates="action_items")

# Create all tables
def create_tables():
    Base.metadata.create_all(bind=engine)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
