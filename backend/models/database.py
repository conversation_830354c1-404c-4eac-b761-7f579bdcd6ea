from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Float, Boolean, ForeignKey, Table, JSON, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
import os

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///db/tagTok.db")

# Create engine
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {}
)

# Create session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# Association table for many-to-many relationship between videos and tags
video_tags = Table(
    'video_tags',
    Base.metadata,
    Column('video_id', Integer, ForeignKey('videos.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True)
)

# Association table for video sharing
video_shares = Table(
    'video_shares',
    Base.metadata,
    Column('id', Integer, primary_key=True),
    Column('video_id', Integer, ForeignKey('videos.id'), nullable=False),
    Column('shared_with_user_id', Integer, ForeignKey('users.id'), nullable=False),
    Column('shared_by_user_id', Integer, ForeignKey('users.id'), nullable=False),
    Column('permissions', String, default='view'),  # view, edit (future)
    Column('shared_date', DateTime(timezone=True), server_default=func.now()),
    Column('expires_at', DateTime(timezone=True), nullable=True)
)

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, unique=True, index=True, nullable=False)
    full_name = Column(String, nullable=True)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    created_date = Column(DateTime(timezone=True), server_default=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)

    # Password reset fields
    reset_token = Column(String, nullable=True)
    reset_token_expires = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    videos = relationship("Video", back_populates="owner", cascade="all, delete-orphan")
    shared_videos = relationship(
        "Video",
        secondary=video_shares,
        primaryjoin="User.id == video_shares.c.shared_with_user_id",
        secondaryjoin="Video.id == video_shares.c.video_id",
        viewonly=True
    )

class Video(Base):
    __tablename__ = "videos"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    filename = Column(String, unique=True, index=True, nullable=False)
    original_filename = Column(String, nullable=False)
    title = Column(String, nullable=True)
    file_path = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)
    duration = Column(Float, nullable=True)
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    fps = Column(Float, nullable=True)
    thumbnail_path = Column(String, nullable=True)
    transcript = Column(Text, nullable=True)
    transcript_language = Column(String, nullable=True)
    upload_date = Column(DateTime(timezone=True), server_default=func.now())
    processed = Column(Boolean, default=False)
    processing_status = Column(String, default="pending")  # pending, processing, completed, failed
    processing_progress = Column(Integer, default=0)  # 0-100 percentage

    # Download-related fields
    source_url = Column(String, nullable=True)  # URL if downloaded from web
    download_status = Column(String, nullable=True)  # downloading, completed, failed
    download_error = Column(Text, nullable=True)  # Error message if download failed
    download_progress = Column(Integer, default=0)  # 0-100 percentage for downloads

    # Relationships
    owner = relationship("User", back_populates="videos")
    tags = relationship("Tag", secondary=video_tags, back_populates="videos")
    recipe = relationship("Recipe", back_populates="video", uselist=False)
    shared_with_users = relationship(
        "User",
        secondary=video_shares,
        primaryjoin="Video.id == video_shares.c.video_id",
        secondaryjoin="User.id == video_shares.c.shared_with_user_id",
        viewonly=True
    )

class VideoShare(Base):
    __tablename__ = "video_shares"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id", ondelete="CASCADE"), nullable=False)
    shared_with_user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    shared_by_user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    permissions = Column(String, default="view")  # view, edit (future)
    shared_date = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    video = relationship("Video")
    shared_with_user = relationship("User", foreign_keys=[shared_with_user_id])
    shared_by_user = relationship("User", foreign_keys=[shared_by_user_id])

class Tag(Base):
    __tablename__ = "tags"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)
    color = Column(String, nullable=False)  # Hex color code
    description = Column(Text, nullable=True)
    created_date = Column(DateTime(timezone=True), server_default=func.now())
    usage_count = Column(Integer, default=0)
    
    # Relationships
    videos = relationship("Video", secondary=video_tags, back_populates="tags")

class ProcessingJob(Base):
    __tablename__ = "processing_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    job_type = Column(String, nullable=False)  # transcription, tagging, thumbnail
    status = Column(String, default="pending")  # pending, running, completed, failed
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Relationship
    video = relationship("Video")

class Recipe(Base):
    __tablename__ = "recipes"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False, unique=True)
    title = Column(String, nullable=True)
    description = Column(Text, nullable=True)

    # Structured recipe data
    ingredients = Column(JSON, nullable=True)  # List of ingredient objects
    instructions = Column(JSON, nullable=True)  # List of instruction steps

    # Additional recipe metadata
    prep_time = Column(String, nullable=True)  # e.g., "15 minutes"
    cook_time = Column(String, nullable=True)  # e.g., "30 minutes"
    total_time = Column(String, nullable=True)  # e.g., "45 minutes"
    servings = Column(String, nullable=True)   # e.g., "4 servings"
    difficulty = Column(String, nullable=True)  # e.g., "Easy", "Medium", "Hard"
    cuisine_type = Column(String, nullable=True)  # e.g., "Italian", "Mexican"

    # Extraction metadata
    extracted_at = Column(DateTime(timezone=True), server_default=func.now())
    extraction_confidence = Column(Float, nullable=True)  # 0.0 to 1.0

    # Relationship
    video = relationship("Video", back_populates="recipe")

# Create all tables
def create_tables():
    Base.metadata.create_all(bind=engine)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
