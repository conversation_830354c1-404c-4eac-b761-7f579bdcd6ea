from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime

# User authentication schemas
class UserBase(BaseModel):
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=50)
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str = Field(..., min_length=8, max_length=100)

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    full_name: Optional[str] = None
    current_password: Optional[str] = None
    new_password: Optional[str] = Field(None, min_length=8, max_length=100)

class UserResponse(UserBase):
    id: int
    is_active: bool
    is_verified: bool
    created_date: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class UserLogin(BaseModel):
    email_or_username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse

class TokenData(BaseModel):
    user_id: Optional[int] = None
    username: Optional[str] = None

# Password reset schemas
class PasswordResetRequest(BaseModel):
    email: EmailStr

class PasswordReset(BaseModel):
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)

# Video sharing schemas
class VideoShareCreate(BaseModel):
    video_id: int
    shared_with_email: str
    permissions: str = "view"
    expires_at: Optional[datetime] = None

class VideoShareUpdate(BaseModel):
    permissions: Optional[str] = None
    expires_at: Optional[datetime] = None

class VideoShareResponse(BaseModel):
    id: int
    video_id: int
    shared_with_user_id: int
    shared_by_user_id: int
    permissions: str
    shared_date: datetime
    expires_at: Optional[datetime] = None
    shared_with_user: UserResponse
    shared_by_user: UserResponse
    
    class Config:
        from_attributes = True

class SharedVideoResponse(BaseModel):
    id: int
    video_id: int
    permissions: str
    shared_date: datetime
    expires_at: Optional[datetime] = None
    shared_by_user: UserResponse
    video_title: Optional[str] = None
    video_thumbnail_path: Optional[str] = None
    video_duration: Optional[float] = None
    
    class Config:
        from_attributes = True

# Bulk sharing operations
class BulkShareRequest(BaseModel):
    video_ids: List[int]
    shared_with_emails: List[str]
    permissions: str = "view"
    expires_at: Optional[datetime] = None

class BulkShareResponse(BaseModel):
    success: bool
    message: str
    processed_count: int
    failed_count: int
    failed_items: List[dict] = []

# User search and management
class UserSearchResponse(BaseModel):
    id: int
    email: str
    username: str
    full_name: Optional[str] = None
    
    class Config:
        from_attributes = True

# Email verification
class EmailVerificationRequest(BaseModel):
    token: str

class ResendVerificationRequest(BaseModel):
    email: EmailStr
