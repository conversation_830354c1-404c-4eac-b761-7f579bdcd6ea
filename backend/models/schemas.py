from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

# Video schemas
class VideoBase(BaseModel):
    title: Optional[str] = None
    original_filename: str

class VideoCreate(VideoBase):
    pass

class VideoUpdate(BaseModel):
    title: Optional[str] = None

class VideoResponse(VideoBase):
    id: int
    filename: str
    file_path: str
    file_size: int
    duration: Optional[float] = None
    width: Optional[int] = None
    height: Optional[int] = None
    fps: Optional[float] = None
    thumbnail_path: Optional[str] = None
    transcript: Optional[str] = None
    transcript_language: Optional[str] = None
    upload_date: datetime
    processed: bool
    processing_status: str
    processing_progress: int = 0
    source_url: Optional[str] = None
    download_status: Optional[str] = None
    download_error: Optional[str] = None
    download_progress: int = 0
    # Summarization fields
    is_meeting: bool = False
    speaker_transcript: Optional[str] = None
    summary_status: str = "none"
    summary_progress: int = 0
    tags: List["TagResponse"] = []
    meeting_summary: Optional["MeetingSummaryResponse"] = None
    speakers: List["SpeakerResponse"] = []

    class Config:
        from_attributes = True

# Tag schemas
class TagBase(BaseModel):
    name: str
    color: str = Field(..., pattern=r'^#[0-9A-Fa-f]{6}$')  # Hex color validation
    description: Optional[str] = None

class TagCreate(TagBase):
    pass

class TagUpdate(BaseModel):
    name: Optional[str] = None
    color: Optional[str] = Field(None, pattern=r'^#[0-9A-Fa-f]{6}$')
    description: Optional[str] = None

class TagResponse(TagBase):
    id: int
    created_date: datetime
    usage_count: int
    
    class Config:
        from_attributes = True

# Processing job schemas
class ProcessingJobResponse(BaseModel):
    id: int
    video_id: int
    job_type: str
    status: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None

    class Config:
        from_attributes = True

# Speaker schemas
class SpeakerBase(BaseModel):
    speaker_label: str
    display_name: Optional[str] = None
    total_speaking_time: Optional[float] = None

class SpeakerCreate(SpeakerBase):
    video_id: int

class SpeakerUpdate(BaseModel):
    display_name: Optional[str] = None

class SpeakerResponse(SpeakerBase):
    id: int
    video_id: int
    created_date: datetime

    class Config:
        from_attributes = True

# Action item schemas
class ActionItemBase(BaseModel):
    description: str
    priority: str = "medium"
    status: str = "pending"
    due_date: Optional[datetime] = None

class ActionItemCreate(ActionItemBase):
    meeting_summary_id: int
    assigned_speaker_id: Optional[int] = None

class ActionItemUpdate(BaseModel):
    description: Optional[str] = None
    priority: Optional[str] = None
    status: Optional[str] = None
    due_date: Optional[datetime] = None
    assigned_speaker_id: Optional[int] = None

class ActionItemResponse(ActionItemBase):
    id: int
    meeting_summary_id: int
    assigned_speaker_id: Optional[int] = None
    created_date: datetime
    updated_date: Optional[datetime] = None
    assigned_speaker: Optional["SpeakerResponse"] = None

    class Config:
        from_attributes = True

# Meeting summary schemas
class MeetingSummaryBase(BaseModel):
    overview: str
    key_points: Optional[str] = None

class MeetingSummaryCreate(MeetingSummaryBase):
    video_id: int

class MeetingSummaryUpdate(BaseModel):
    overview: Optional[str] = None
    key_points: Optional[str] = None

class MeetingSummaryResponse(MeetingSummaryBase):
    id: int
    video_id: int
    created_date: datetime
    updated_date: Optional[datetime] = None
    action_items: List["ActionItemResponse"] = []

    class Config:
        from_attributes = True

# Search and filter schemas
class VideoFilter(BaseModel):
    tags: Optional[List[str]] = None
    search: Optional[str] = None
    language: Optional[str] = None
    processed: Optional[bool] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    category: Optional[str] = None  # Filter by tag category (Business, Tech, Design, etc.)

class PaginationParams(BaseModel):
    skip: int = Field(0, ge=0)
    limit: int = Field(50, ge=1, le=100)

# Analytics schemas
class AnalyticsResponse(BaseModel):
    total_videos: int
    total_tags: int
    total_duration: float
    processed_videos: int
    pending_videos: int
    top_tags: List[Dict[str, Any]]
    language_distribution: Dict[str, int]
    upload_timeline: List[Dict[str, Any]]
    duration_distribution: Dict[str, int]

# Export schemas
class ExportFormat(BaseModel):
    format: str = Field(..., pattern=r'^(csv|json)$')
    include_transcript: bool = True
    include_tags: bool = True

# Upload response
class UploadResponse(BaseModel):
    message: str
    uploaded_files: List[str]
    failed_files: List[Dict[str, str]]
    total_uploaded: int

# Download request
class DownloadRequest(BaseModel):
    url: str = Field(..., description="URL to download video from")
    quality: Optional[str] = Field(default="best", description="Video quality preference")
    format: Optional[str] = Field(default="mp4", description="Preferred video format")

# Download response
class DownloadResponse(BaseModel):
    message: str
    video_id: int
    download_status: str
    url: str

# Error response
class ErrorResponse(BaseModel):
    detail: str
    error_code: Optional[str] = None

# Health check response
class HealthResponse(BaseModel):
    status: str
    timestamp: datetime
    version: str = "1.0.0"

# Bulk operations schemas
class BulkTagOperation(BaseModel):
    video_ids: List[int] = Field(..., min_items=1, description="List of video IDs to operate on")
    tag_ids: List[int] = Field(..., min_items=1, description="List of tag IDs to add or remove")

class BulkDeleteRequest(BaseModel):
    video_ids: List[int] = Field(..., min_items=1, description="List of video IDs to delete")

class BulkOperationResponse(BaseModel):
    success: bool
    message: str
    processed_count: int
    failed_count: int
    failed_items: List[Dict[str, Any]] = []

class BulkTagOperationResponse(BulkOperationResponse):
    updated_videos: List[int] = []

class BulkDeleteResponse(BulkOperationResponse):
    deleted_videos: List[int] = []

# Summarization-specific schemas
class SummarizationRequest(BaseModel):
    video_id: int
    enable_speaker_diarization: bool = True

class SummarizationResponse(BaseModel):
    message: str
    video_id: int
    job_id: int
    estimated_completion_time: Optional[str] = None

class SummarizationStatus(BaseModel):
    video_id: int
    status: str  # none, processing, completed, failed
    progress: int  # 0-100
    error_message: Optional[str] = None
    estimated_completion_time: Optional[str] = None

# Update forward references
VideoResponse.model_rebuild()
MeetingSummaryResponse.model_rebuild()
ActionItemResponse.model_rebuild()
