import os
import secrets
from datetime import datetime, timedelta
from typing import Op<PERSON>, Union
from passlib.context import Crypt<PERSON>ontext
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from fastapi import HTT<PERSON>Exception, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from models.database import get_db, User
from models.auth_schemas import TokenData

# Security configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30 * 24 * 60  # 30 days

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token security
security = HTTPBearer()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash a password"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    # Ensure 'sub' is a string as required by JWT spec
    if 'sub' in to_encode and isinstance(to_encode['sub'], int):
        to_encode['sub'] = str(to_encode['sub'])

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[TokenData]:
    """Verify and decode a JWT token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id_str: str = payload.get("sub")
        username: str = payload.get("username")

        if user_id_str is None:
            print(f"DEBUG: Token validation failed - no user_id in payload: {payload}")
            return None

        # Convert string user_id back to int
        try:
            user_id = int(user_id_str)
        except (ValueError, TypeError):
            print(f"DEBUG: Invalid user_id format: {user_id_str}")
            return None

        token_data = TokenData(user_id=user_id, username=username)
        return token_data
    except JWTError as e:
        print(f"DEBUG: JWT Error during token validation: {e}")
        print(f"DEBUG: Token: {token[:50]}...")
        print(f"DEBUG: SECRET_KEY: {SECRET_KEY[:20]}...")
        return None

def generate_reset_token() -> str:
    """Generate a secure random token for password reset"""
    return secrets.token_urlsafe(32)

def authenticate_user(db: Session, email_or_username: str, password: str) -> Optional[User]:
    """Authenticate a user by email/username and password"""
    # Try to find user by email first, then by username
    user = db.query(User).filter(User.email == email_or_username).first()
    if not user:
        user = db.query(User).filter(User.username == email_or_username).first()
    
    if not user:
        return None
    
    if not verify_password(password, user.hashed_password):
        return None
    
    return user

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Get the current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        token = credentials.credentials
        token_data = verify_token(token)
        
        if token_data is None or token_data.user_id is None:
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception
    
    user = db.query(User).filter(User.id == token_data.user_id).first()
    if user is None:
        raise credentials_exception
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get the current active user"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

def check_video_access(user: User, video, permission: str = "view") -> bool:
    """Check if user has access to a video"""
    # Owner has full access
    if video.user_id == user.id:
        return True
    
    # Check if video is shared with user
    from models.database import VideoShare
    db = Session.object_session(user)
    
    share = db.query(VideoShare).filter(
        VideoShare.video_id == video.id,
        VideoShare.shared_with_user_id == user.id
    ).first()
    
    if not share:
        return False
    
    # Check if share has expired
    if share.expires_at and share.expires_at < datetime.utcnow():
        return False
    
    # For now, we only support 'view' permission
    # In the future, we can add 'edit' permission checks
    return permission == "view"

def require_video_access(permission: str = "view"):
    """Decorator to require video access"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # This will be used in route handlers
            # The actual implementation will be in the route handlers
            return func(*args, **kwargs)
        return wrapper
    return decorator
