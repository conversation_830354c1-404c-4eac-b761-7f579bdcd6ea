import axios from 'axios';
import {
  Video,
  Tag,
  AnalyticsData,
  UploadResponse,
  VideoFilter,
  PaginationParams,
  VideoUpdateData,
  TagCreateData,
  TagUpdateData,
  ExportOptions,
  CategoryResponse,
  DownloadRequest,
  DownloadResponse,
  Recipe,
  RecipeWithVideo
} from '../types';
import { apiCircuitBreaker } from './circuitBreaker';

// Cached API base URL to avoid recalculating on every request
let cachedApiBaseUrl: string | null = null;

// Mobile-friendly API URL detection (cached for performance)
const getApiBaseUrl = () => {
  // Return cached URL if already calculated
  if (cachedApiBaseUrl) {
    return cachedApiBaseUrl;
  }

  // Get current location details
  const hostname = window.location.hostname;
  const protocol = window.location.protocol;

  // Check if accessing via IP address (local network access)
  const isIpAddress = /^\d+\.\d+\.\d+\.\d+$/.test(hostname);

  // If accessing via IP address, always use local API endpoint to avoid CORS
  if (isIpAddress) {
    // Check if we're accessing through port 3001 (development frontend)
    if (window.location.port === '3001') {
      // Direct backend access (no /api prefix)
      cachedApiBaseUrl = `${protocol}//${hostname}:8090`;
    } else {
      // Through nginx proxy (with /api prefix)
      cachedApiBaseUrl = `${protocol}//${hostname}:8790/api`;
    }
    console.log('API Base URL determined (IP access):', cachedApiBaseUrl);
    return cachedApiBaseUrl;
  }

  // Check environment variable for domain-based access
  const envApiUrl = process.env.REACT_APP_API_URL;
  if (envApiUrl && (hostname !== 'localhost' && hostname !== '127.0.0.1')) {
    cachedApiBaseUrl = envApiUrl;
    console.log('API Base URL determined (env var):', cachedApiBaseUrl);
    return envApiUrl;
  }

  // Default to localhost for desktop development
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    cachedApiBaseUrl = 'http://localhost:8790/api';
  } else {
    // Fallback for other domain access
    cachedApiBaseUrl = `${protocol}//${hostname}/api`;
  }

  // Log the determined URL only once
  console.log('API Base URL determined (fallback):', cachedApiBaseUrl);
  return cachedApiBaseUrl;
};

const api = axios.create({
  timeout: 15000, // 15 second timeout - fail very fast when server is hanging
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for dynamic base URL and auth token
api.interceptors.request.use((config) => {
  // Set the base URL for each request (cached for performance)
  config.baseURL = getApiBaseUrl();

  // Add auth token if available
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Only log errors, not every successful request
    if (error.response?.status >= 500) {
      console.error('Server Error:', error.response?.data || error.message);
    } else if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
      console.error('Network Error: Cannot reach the server');
    }
    return Promise.reject(error);
  }
);

// Video API
// Mobile connection test function
export const testMobileConnection = async () => {
  try {
    const currentApiBaseUrl = getApiBaseUrl();
    const response = await api.get('/health');
    console.log('Connection test successful:', currentApiBaseUrl);
    return { success: true, data: response.data, apiUrl: currentApiBaseUrl };
  } catch (error: any) {
    console.error('Connection test failed:', error.message);
    return {
      success: false,
      error: error.message,
      apiUrl: getApiBaseUrl(),
      details: {
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url,
        baseURL: error.config?.baseURL,
        fullUrl: error.config?.baseURL + error.config?.url
      }
    };
  }
};

export const videoApi = {
  // Get videos with filtering and pagination
  getVideos: async (
    pagination: PaginationParams = { skip: 0, limit: 50 },
    filters?: VideoFilter
  ): Promise<Video[]> => {
    const params = new URLSearchParams();
    params.append('skip', pagination.skip.toString());
    params.append('limit', pagination.limit.toString());

    if (filters?.tags?.length) {
      params.append('tags', filters.tags.join(','));
    }
    if (filters?.search) {
      params.append('search', filters.search);
    }
    if (filters?.language) {
      params.append('language', filters.language);
    }
    if (filters?.processed !== undefined) {
      params.append('processed', filters.processed.toString());
    }
    if (filters?.category) {
      params.append('category', filters.category);
    }

    const url = `/videos?${params.toString()}`;
    console.log('Making API request to:', url);

    try {
      const response = await api.get(url);
      console.log('API response received:', response.status, response.data?.length || 0, 'videos');
      return response.data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  },

  // Get single video
  getVideo: async (id: number): Promise<Video> => {
    const response = await api.get(`/videos/${id}`);
    return response.data;
  },

  // Update video
  updateVideo: async (id: number, data: VideoUpdateData): Promise<Video> => {
    const response = await api.put(`/videos/${id}`, data);
    return response.data;
  },

  // Delete video
  deleteVideo: async (id: number): Promise<void> => {
    await api.delete(`/videos/${id}`);
  },

  // Upload videos
  uploadVideos: async (
    files: FileList,
    onProgress?: (progress: number) => void
  ): Promise<UploadResponse> => {
    console.log(`Starting upload of ${files.length} files to ${getApiBaseUrl()}/videos/upload`);

    const formData = new FormData();
    Array.from(files).forEach((file, index) => {
      console.log(`Adding file ${index + 1}: ${file.name} (${file.size} bytes, ${file.type})`);
      formData.append('files', file);
    });

    try {
      const response = await api.post('/videos/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 600000, // 10 minutes for large uploads
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total && onProgress) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            console.log(`Upload progress: ${progress}% (${progressEvent.loaded}/${progressEvent.total} bytes)`);
            onProgress(progress);
          }
        },
      });
      console.log('Upload completed successfully:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Upload failed:', {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          baseURL: error.config?.baseURL,
          timeout: error.config?.timeout
        }
      });
      throw error;
    }
  },

  // Get video transcript
  getTranscript: async (id: number): Promise<{ video_id: number; transcript: string; language: string }> => {
    const response = await api.get(`/videos/${id}/transcript`);
    return response.data;
  },

  // Reprocess video
  reprocessVideo: async (id: number): Promise<void> => {
    await api.post(`/videos/${id}/reprocess`);
  },

  // Download video file
  downloadVideoFile: async (id: number): Promise<Blob> => {
    const response = await api.get(`/videos/${id}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Download video from URL
  downloadVideo: async (downloadRequest: DownloadRequest): Promise<DownloadResponse> => {
    const response = await api.post('/videos/download', downloadRequest);
    return response.data;
  },

  // Get download status
  getDownloadStatus: async (id: number): Promise<any> => {
    const response = await api.get(`/videos/${id}/download-status`);
    return response.data;
  },

  // Get processing status (lightweight for polling)
  getProcessingStatus: async (id: number): Promise<{
    video_id: number;
    processing_status: string;
    processing_progress: number;
    processed: boolean;
  }> => {
    // Temporarily disable circuit breaker for processing status to avoid blocking
    const response = await api.get(`/videos/${id}/processing-status`);
    return response.data;
  },

  // Get video file URL
  getVideoUrl: (filename: string): string => {
    return `${getApiBaseUrl()}/videos/file/${filename}`;
  },

  // Get thumbnail URL
  getThumbnailUrl: (filename: string): string => {
    return `${getApiBaseUrl()}/videos/thumbnail/${filename}`;
  },

  // Get available categories
  getCategories: async (): Promise<string[]> => {
    const response = await api.get('/videos/categories');
    return response.data.categories;
  },

  // Bulk operations
  bulkAddTags: async (videoIds: number[], tagIds: number[]): Promise<any> => {
    const response = await api.post('/videos/bulk/add-tags', {
      video_ids: videoIds,
      tag_ids: tagIds,
    });
    return response.data;
  },

  bulkRemoveTags: async (videoIds: number[], tagIds: number[]): Promise<any> => {
    const response = await api.post('/videos/bulk/remove-tags', {
      video_ids: videoIds,
      tag_ids: tagIds,
    });
    return response.data;
  },

  bulkDeleteVideos: async (videoIds: number[]): Promise<any> => {
    const response = await api.delete('/videos/bulk', {
      data: {
        video_ids: videoIds,
      },
    });
    return response.data;
  },
};

// Tag API
export const tagApi = {
  // Get tags
  getTags: async (
    pagination: PaginationParams = { skip: 0, limit: 100 },
    search?: string,
    sortBy: string = 'usage_count',
    order: string = 'desc'
  ): Promise<Tag[]> => {
    const params = new URLSearchParams();
    params.append('skip', pagination.skip.toString());
    params.append('limit', pagination.limit.toString());
    params.append('sort_by', sortBy);
    params.append('order', order);
    
    if (search) {
      params.append('search', search);
    }
    
    const response = await api.get(`/tags?${params.toString()}`);
    return response.data;
  },

  // Get single tag
  getTag: async (id: number): Promise<Tag> => {
    const response = await api.get(`/tags/${id}`);
    return response.data;
  },

  // Create tag
  createTag: async (data: TagCreateData): Promise<Tag> => {
    const response = await api.post('/tags', data);
    return response.data;
  },

  // Update tag
  updateTag: async (id: number, data: TagUpdateData): Promise<Tag> => {
    const response = await api.put(`/tags/${id}`, data);
    return response.data;
  },

  // Delete tag
  deleteTag: async (id: number): Promise<void> => {
    await api.delete(`/tags/${id}`);
  },

  // Add tag to video
  addTagToVideo: async (tagId: number, videoId: number): Promise<void> => {
    await api.post(`/tags/${tagId}/videos/${videoId}`);
  },

  // Remove tag from video
  removeTagFromVideo: async (tagId: number, videoId: number): Promise<void> => {
    await api.delete(`/tags/${tagId}/videos/${videoId}`);
  },

  // Get tag cloud data
  getTagCloudData: async () => {
    const response = await api.get('/tags/cloud/data');
    return response.data;
  },
};

// Analytics API
export const analyticsApi = {
  // Get analytics
  getAnalytics: async (days: number = 30): Promise<AnalyticsData> => {
    const response = await api.get(`/analytics?days=${days}`);
    return response.data;
  },

  // Get summary
  getSummary: async () => {
    const response = await api.get('/analytics/summary');
    return response.data;
  },

  // Get top tags
  getTopTags: async (limit: number = 10) => {
    const response = await api.get(`/analytics/tags/top?limit=${limit}`);
    return response.data;
  },

  // Get upload timeline
  getUploadTimeline: async (days: number = 30) => {
    const response = await api.get(`/analytics/videos/timeline?days=${days}`);
    return response.data;
  },

  // Get duration distribution
  getDurationDistribution: async () => {
    const response = await api.get('/analytics/videos/duration-distribution');
    return response.data;
  },

  // Get language distribution
  getLanguageDistribution: async () => {
    const response = await api.get('/analytics/languages');
    return response.data;
  },

  // Get processing status
  getProcessingStatus: async () => {
    const response = await api.get('/analytics/processing-status');
    return response.data;
  },
};

// Export API
export const exportApi = {
  // Export videos
  exportVideos: async (options: ExportOptions): Promise<Blob> => {
    const params = new URLSearchParams();
    params.append('format', options.format);
    params.append('include_transcript', options.include_transcript.toString());
    params.append('include_tags', options.include_tags.toString());
    
    if (options.tag_filter?.length) {
      params.append('tag_filter', options.tag_filter.join(','));
    }
    
    const response = await api.get(`/export/videos?${params.toString()}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Export tags
  exportTags: async (format: 'csv' | 'json' = 'csv'): Promise<Blob> => {
    const response = await api.get(`/export/tags?format=${format}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Export analytics
  exportAnalytics: async (format: 'json' = 'json', days: number = 30): Promise<Blob> => {
    const response = await api.get(`/export/analytics?format=${format}&days=${days}`, {
      responseType: 'blob',
    });
    return response.data;
  },
};

// Recipe API endpoints
export const recipeApi = {
  // Get all recipes with optional filtering
  getAll: async (params?: {
    skip?: number;
    limit?: number;
    cuisine?: string;
    difficulty?: string;
    search?: string;
  }): Promise<Recipe[]> => {
    const response = await api.get('/recipes/', { params });
    return response.data;
  },

  // Get all recipes with video information for the recipes page
  getAllWithVideos: async (params?: {
    skip?: number;
    limit?: number;
    cuisine?: string;
    difficulty?: string;
    search?: string;
  }): Promise<RecipeWithVideo[]> => {
    const response = await api.get('/recipes/with-videos', { params });
    return response.data;
  },

  // Get recipe by ID
  getById: async (id: number): Promise<Recipe> => {
    const response = await api.get(`/recipes/${id}`);
    return response.data;
  },

  // Get recipe for specific video
  getByVideoId: async (videoId: number): Promise<Recipe> => {
    const response = await api.get(`/recipes/video/${videoId}`);
    return response.data;
  },

  // Extract recipe from video
  extractFromVideo: async (videoId: number): Promise<{ message: string; recipe?: Recipe }> => {
    const response = await api.post(`/recipes/video/${videoId}/extract`);
    return response.data;
  },

  // Search recipes by ingredients
  searchByIngredients: async (ingredients: string): Promise<Recipe[]> => {
    const response = await api.get('/recipes/search/ingredients', {
      params: { ingredients }
    });
    return response.data;
  },

  // Get recipe statistics
  getStats: async (): Promise<{
    total_recipes: number;
    difficulty_breakdown: { easy: number; medium: number; hard: number };
    cuisine_types: number;
    avg_confidence: number;
  }> => {
    const response = await api.get('/recipes/stats/overview');
    return response.data;
  },

  // Get available cuisine types
  getCuisines: async (): Promise<string[]> => {
    const response = await api.get('/recipes/cuisines/list');
    return response.data;
  },

  // Get available difficulty levels
  getDifficulties: async (): Promise<string[]> => {
    const response = await api.get('/recipes/difficulties/list');
    return response.data;
  },

  // Update recipe
  update: async (id: number, data: Partial<Recipe>): Promise<Recipe> => {
    const response = await api.put(`/recipes/${id}`, data);
    return response.data;
  },

  // Delete recipe
  delete: async (id: number): Promise<{ message: string }> => {
    const response = await api.delete(`/recipes/${id}`);
    return response.data;
  },
};

// Health check
export const healthApi = {
  check: async () => {
    const response = await api.get('/health');
    return response.data;
  },
};

// Authentication API endpoints
export const authApi = {
  // Register new user
  register: async (userData: {
    email: string;
    username: string;
    password: string;
    full_name?: string;
  }) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  // Login user
  login: async (emailOrUsername: string, password: string) => {
    const response = await api.post('/auth/login', {
      email_or_username: emailOrUsername,
      password
    });
    return response.data;
  },

  // Get current user info
  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  // Update user profile
  updateProfile: async (userData: any) => {
    const response = await api.put('/auth/me', userData);
    return response.data;
  },

  // Request password reset
  forgotPassword: async (email: string) => {
    const response = await api.post('/auth/forgot-password', { email });
    return response.data;
  },

  // Reset password with token
  resetPassword: async (token: string, newPassword: string) => {
    const response = await api.post('/auth/reset-password', {
      token,
      new_password: newPassword
    });
    return response.data;
  },

  // Search users for sharing
  searchUsers: async (query: string) => {
    const response = await api.get(`/auth/users/search?q=${encodeURIComponent(query)}`);
    return response.data;
  }
};

// Video sharing API endpoints
export const sharingApi = {
  // Share video with user
  shareVideo: async (videoId: number, sharedWithEmail: string, permissions = 'view') => {
    const response = await api.post(`/sharing/videos/${videoId}/share`, {
      video_id: videoId,
      shared_with_email: sharedWithEmail,
      permissions
    });
    return response.data;
  },

  // Get video shares
  getVideoShares: async (videoId: number) => {
    const response = await api.get(`/sharing/videos/${videoId}/shares`);
    return response.data;
  },

  // Update video share
  updateVideoShare: async (shareId: number, data: any) => {
    const response = await api.put(`/sharing/shares/${shareId}`, data);
    return response.data;
  },

  // Remove video share
  removeVideoShare: async (shareId: number) => {
    await api.delete(`/sharing/shares/${shareId}`);
  },

  // Get videos shared with me
  getSharedVideos: async () => {
    const response = await api.get('/sharing/shared-with-me');
    return response.data;
  },

  // Bulk share videos
  bulkShareVideos: async (videoIds: number[], emails: string[], permissions = 'view') => {
    const response = await api.post('/sharing/videos/bulk-share', {
      video_ids: videoIds,
      shared_with_emails: emails,
      permissions
    });
    return response.data;
  }
};

export default api;
