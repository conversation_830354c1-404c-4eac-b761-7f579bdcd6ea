import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  CloudArrowUpIcon,
  DocumentIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  UserGroupIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

import { summarizationApi } from '../utils/api';
import { UploadResponse } from '../types';
import FileDropzone from '../components/FileDropzone';
import LoadingSpinner from '../components/LoadingSpinner';

interface UploadFile {
  file: File;
  id: string;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

const SummarizeUploadPage: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState(false);

  const uploadMutation = useMutation({
    mutationFn: (files: FileList) => {
      setIsUploading(true);
      setUploadProgress(0);
      return summarizationApi.uploadMeetingVideos(files, (progress) => {
        setUploadProgress(progress);
      });
    },
    onSuccess: (data: UploadResponse) => {
      setIsUploading(false);
      setUploadProgress(100);
      
      // Update file statuses
      setUploadFiles(prev => prev.map(file => {
        if (data.uploaded_files.includes(file.file.name)) {
          return { ...file, status: 'success' };
        } else {
          const failedFile = data.failed_files.find(f => f.filename === file.file.name);
          return { 
            ...file, 
            status: 'error',
            error: failedFile?.error || 'Upload failed'
          };
        }
      }));

      // Show success message
      if (data.total_uploaded > 0) {
        toast.success(`Successfully uploaded ${data.total_uploaded} meeting video(s)! Processing will begin automatically.`);
        
        // Invalidate queries to refresh data
        queryClient.invalidateQueries({ queryKey: ['meeting-videos'] });
        
        // Navigate to summarize page after a delay
        setTimeout(() => {
          navigate('/summarize');
        }, 2000);
      }

      // Show error messages for failed uploads
      if (data.failed_files.length > 0) {
        data.failed_files.forEach(failedFile => {
          toast.error(`Failed to upload ${failedFile.filename}: ${failedFile.error}`);
        });
      }
    },
    onError: (error: any) => {
      setIsUploading(false);
      setUploadProgress(0);
      
      // Update all files to error status
      setUploadFiles(prev => prev.map(file => ({ 
        ...file, 
        status: 'error',
        error: 'Upload failed'
      })));
      
      toast.error('Upload failed. Please try again.');
      console.error('Upload error:', error);
    }
  });

  const handleFilesSelected = (files: FileList) => {
    const newUploadFiles: UploadFile[] = Array.from(files).map(file => ({
      file,
      id: `${file.name}-${Date.now()}`,
      status: 'pending',
    }));

    setUploadFiles(prev => [...prev, ...newUploadFiles]);
  };

  const handleRemoveFile = (id: string) => {
    setUploadFiles(prev => prev.filter(file => file.id !== id));
  };

  const handleUpload = () => {
    if (uploadFiles.length === 0) return;

    // Create FileList from upload files
    const dt = new DataTransfer();
    uploadFiles.forEach(uploadFile => {
      dt.items.add(uploadFile.file);
    });

    // Update status to uploading
    setUploadFiles(prev => prev.map(file => ({ ...file, status: 'uploading' })));

    uploadMutation.mutate(dt.files);
  };

  const handleClearAll = () => {
    setUploadFiles([]);
    setUploadProgress(0);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'uploading':
        return <LoadingSpinner size="sm" />;
      default:
        return <DocumentIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
          <DocumentTextIcon className="h-8 w-8 mr-3 text-primary-600 dark:text-primary-400" />
          Upload Meeting Videos
        </h1>
        <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
          Upload your meeting recordings to automatically generate transcripts with speaker identification, 
          AI-powered summaries, and action items
        </p>
      </div>

      {/* Features Info */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
          What happens after upload:
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800 dark:text-blue-200">
          <div className="flex items-center">
            <UserGroupIcon className="h-4 w-4 mr-2" />
            Speaker identification
          </div>
          <div className="flex items-center">
            <DocumentTextIcon className="h-4 w-4 mr-2" />
            AI-powered summary
          </div>
          <div className="flex items-center">
            <ClockIcon className="h-4 w-4 mr-2" />
            Action item extraction
          </div>
        </div>
      </div>

      {/* Upload Area */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <FileDropzone
          onFilesSelected={handleFilesSelected}
          accept="video/*"
          multiple
          disabled={uploadMutation.isPending}
        />
      </div>

      {/* File List */}
      {uploadFiles.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Selected Files ({uploadFiles.length})
            </h3>
            <div className="flex space-x-2">
              {!isUploading && (
                <button
                  onClick={handleClearAll}
                  className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  Clear All
                </button>
              )}
            </div>
          </div>
          
          <div className="p-6 space-y-3 max-h-64 overflow-y-auto">
            {uploadFiles.map((uploadFile) => (
              <div
                key={uploadFile.id}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  {getStatusIcon(uploadFile.status)}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {uploadFile.file.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {formatFileSize(uploadFile.file.size)}
                      {uploadFile.error && (
                        <span className="text-red-500 ml-2">• {uploadFile.error}</span>
                      )}
                    </p>
                  </div>
                </div>
                
                {!isUploading && uploadFile.status === 'pending' && (
                  <button
                    onClick={() => handleRemoveFile(uploadFile.id)}
                    className="ml-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                )}
              </div>
            ))}
          </div>

          {/* Upload Progress */}
          {isUploading && (
            <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Uploading files...
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {uploadProgress}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                <div
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
            </div>
          )}

          {/* Upload Button */}
          {!isUploading && uploadFiles.some(f => f.status === 'pending') && (
            <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={handleUpload}
                disabled={uploadFiles.length === 0 || uploadMutation.isPending}
                className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <CloudArrowUpIcon className="h-5 w-5 mr-2" />
                Upload Meeting Videos
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SummarizeUploadPage;
