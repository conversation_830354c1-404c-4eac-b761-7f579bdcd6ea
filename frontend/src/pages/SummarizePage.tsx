import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  DocumentTextIcon,
  CloudArrowUpIcon,
  PlayIcon,
  UserGroupIcon,
  ClockIcon,
  TagIcon,
  EyeIcon,
  TrashIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

import { summarizationApi } from '../utils/api';
import { VideoResponse } from '../types';
import LoadingSpinner from '../components/LoadingSpinner';

const SummarizePage: React.FC = () => {
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  const { data: meetingVideos, isLoading, error } = useQuery({
    queryKey: ['meeting-videos', selectedStatus],
    queryFn: () => summarizationApi.getMeetingVideos({
      skip: 0,
      limit: 50,
      status: selectedStatus === 'all' ? undefined : selectedStatus
    }),
    refetchInterval: 5000, // Refresh every 5 seconds to show processing progress
  });

  const getStatusBadge = (video: VideoResponse) => {
    const status = video.summary_status;
    const progress = video.summary_progress;

    switch (status) {
      case 'completed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            Completed
          </span>
        );
      case 'processing':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            Processing ({progress}%)
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Failed
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            Pending
          </span>
        );
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">
          Error loading meeting videos. Please try again.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
            <DocumentTextIcon className="h-8 w-8 mr-3 text-primary-600 dark:text-primary-400" />
            Meeting Summaries
          </h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            AI-powered meeting summaries with speaker identification and action items
          </p>
        </div>
        
        <Link
          to="/summarize/upload"
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <CloudArrowUpIcon className="h-5 w-5 mr-2" />
          Upload Meeting
        </Link>
      </div>

      {/* Status Filter */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Filter by status:
          </span>
          <div className="flex space-x-2">
            {[
              { value: 'all', label: 'All' },
              { value: 'completed', label: 'Completed' },
              { value: 'processing', label: 'Processing' },
              { value: 'failed', label: 'Failed' },
              { value: 'none', label: 'Pending' }
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => setSelectedStatus(option.value)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  selectedStatus === option.value
                    ? 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Meeting Videos Grid */}
      {meetingVideos && meetingVideos.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {meetingVideos.map((video) => (
            <div
              key={video.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition-shadow"
            >
              {/* Video Thumbnail */}
              <div className="relative aspect-video bg-gray-200 dark:bg-gray-700 rounded-t-lg overflow-hidden">
                {video.thumbnail_path ? (
                  <img
                    src={`/api/videos/thumbnail/${video.thumbnail_path.split('/').pop()}`}
                    alt={video.title || video.original_filename}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <PlayIcon className="h-12 w-12 text-gray-400" />
                  </div>
                )}
                
                {/* Status Badge */}
                <div className="absolute top-2 right-2">
                  {getStatusBadge(video)}
                </div>
                
                {/* Duration */}
                {video.duration && (
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                    {formatDuration(video.duration)}
                  </div>
                )}
              </div>

              {/* Video Info */}
              <div className="p-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 truncate">
                  {video.title || video.original_filename}
                </h3>
                
                <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-2" />
                    {new Date(video.upload_date).toLocaleDateString()}
                  </div>
                  
                  {video.speakers && video.speakers.length > 0 && (
                    <div className="flex items-center">
                      <UserGroupIcon className="h-4 w-4 mr-2" />
                      {video.speakers.length} speaker{video.speakers.length !== 1 ? 's' : ''}
                    </div>
                  )}
                  
                  {video.tags && video.tags.length > 0 && (
                    <div className="flex items-center">
                      <TagIcon className="h-4 w-4 mr-2" />
                      {video.tags.length} tag{video.tags.length !== 1 ? 's' : ''}
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="mt-4 flex space-x-2">
                  <Link
                    to={`/video/${video.id}`}
                    className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    <EyeIcon className="h-4 w-4 mr-2" />
                    View Details
                  </Link>
                  
                  {video.summary_status === 'completed' && video.meeting_summary && (
                    <Link
                      to={`/summarize/summary/${video.id}`}
                      className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <DocumentTextIcon className="h-4 w-4 mr-2" />
                      Summary
                    </Link>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No meeting videos
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Get started by uploading your first meeting recording.
          </p>
          <div className="mt-6">
            <Link
              to="/summarize/upload"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Upload Meeting Video
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default SummarizePage;
