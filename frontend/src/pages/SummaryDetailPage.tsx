import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  DocumentTextIcon,
  UserGroupIcon,
  ClockIcon,
  TagIcon,
  PlayIcon,
  ArrowLeftIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';

import { summarizationApi } from '../utils/api';
import { VideoResponse, MeetingSummary, ActionItem, Speaker } from '../types';
import LoadingSpinner from '../components/LoadingSpinner';
import SpeakerTranscriptViewer from '../components/SpeakerTranscriptViewer';

const SummaryDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const videoId = parseInt(id || '0');

  const [isEditingOverview, setIsEditingOverview] = useState(false);
  const [editOverview, setEditOverview] = useState('');
  const [newActionItem, setNewActionItem] = useState('');
  const [showAddAction, setShowAddAction] = useState(false);

  // Fetch meeting video details
  const { data: video, isLoading: videoLoading } = useQuery({
    queryKey: ['meeting-video', videoId],
    queryFn: () => summarizationApi.getMeetingVideo(videoId),
    enabled: !!videoId,
  });

  // Fetch meeting summary
  const { data: summary, isLoading: summaryLoading, error: summaryError } = useQuery({
    queryKey: ['meeting-summary', videoId],
    queryFn: () => summarizationApi.getMeetingSummary(videoId),
    enabled: !!videoId,
  });

  // Fetch action items
  const { data: actionItems = [], isLoading: actionsLoading } = useQuery({
    queryKey: ['action-items', videoId],
    queryFn: () => summarizationApi.getActionItems(videoId),
    enabled: !!videoId,
  });

  // Update summary mutation
  const updateSummaryMutation = useMutation({
    mutationFn: (data: { overview: string }) => 
      summarizationApi.updateMeetingSummary(videoId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meeting-summary', videoId] });
      toast.success('Summary updated successfully');
      setIsEditingOverview(false);
    },
    onError: () => {
      toast.error('Failed to update summary');
    }
  });

  // Create action item mutation
  const createActionMutation = useMutation({
    mutationFn: (description: string) => 
      summarizationApi.createActionItem(videoId, { description }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['action-items', videoId] });
      toast.success('Action item added');
      setNewActionItem('');
      setShowAddAction(false);
    },
    onError: () => {
      toast.error('Failed to add action item');
    }
  });

  // Delete action item mutation
  const deleteActionMutation = useMutation({
    mutationFn: (actionId: number) => 
      summarizationApi.deleteActionItem(actionId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['action-items', videoId] });
      toast.success('Action item deleted');
    },
    onError: () => {
      toast.error('Failed to delete action item');
    }
  });

  const handleUpdateOverview = () => {
    if (editOverview.trim()) {
      updateSummaryMutation.mutate({ overview: editOverview.trim() });
    }
  };

  const handleAddActionItem = () => {
    if (newActionItem.trim()) {
      createActionMutation.mutate(newActionItem.trim());
    }
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const parseKeyPoints = (keyPointsJson?: string): string[] => {
    if (!keyPointsJson) return [];
    try {
      return JSON.parse(keyPointsJson);
    } catch {
      return [];
    }
  };

  if (videoLoading || summaryLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!video) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">Video not found</p>
        <Link to="/summarize" className="text-primary-600 hover:text-primary-700 mt-2 inline-block">
          ← Back to Summaries
        </Link>
      </div>
    );
  }

  if (summaryError || !summary) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">
          Meeting summary not available. The video may still be processing.
        </p>
        <Link to="/summarize" className="text-primary-600 hover:text-primary-700 mt-2 inline-block">
          ← Back to Summaries
        </Link>
      </div>
    );
  }

  const keyPoints = parseKeyPoints(summary.key_points);

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/summarize"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Summaries
          </Link>
        </div>
        <Link
          to={`/video/${video.id}`}
          className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          <PlayIcon className="h-4 w-4 mr-2" />
          View Video
        </Link>
      </div>

      {/* Video Info */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {video.title || video.original_filename}
            </h1>
            <div className="flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center">
                <ClockIcon className="h-4 w-4 mr-1" />
                {formatDate(video.upload_date)}
              </div>
              {video.duration && (
                <div className="flex items-center">
                  <PlayIcon className="h-4 w-4 mr-1" />
                  {formatDuration(video.duration)}
                </div>
              )}
              {video.speakers && (
                <div className="flex items-center">
                  <UserGroupIcon className="h-4 w-4 mr-1" />
                  {video.speakers.length} speaker{video.speakers.length !== 1 ? 's' : ''}
                </div>
              )}
            </div>
          </div>
          
          {video.thumbnail_path && (
            <img
              src={`/api/videos/thumbnail/${video.thumbnail_path.split('/').pop()}`}
              alt={video.title || video.original_filename}
              className="w-32 h-18 object-cover rounded-lg"
            />
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Meeting Overview */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Meeting Overview
              </h2>
              {!isEditingOverview && (
                <button
                  onClick={() => {
                    setEditOverview(summary.overview);
                    setIsEditingOverview(true);
                  }}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                >
                  <PencilIcon className="h-4 w-4" />
                </button>
              )}
            </div>
            
            {isEditingOverview ? (
              <div className="space-y-3">
                <textarea
                  value={editOverview}
                  onChange={(e) => setEditOverview(e.target.value)}
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  rows={4}
                />
                <div className="flex space-x-2">
                  <button
                    onClick={handleUpdateOverview}
                    disabled={updateSummaryMutation.isPending}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"
                  >
                    <CheckIcon className="h-4 w-4 mr-1" />
                    Save
                  </button>
                  <button
                    onClick={() => setIsEditingOverview(false)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                  >
                    <XMarkIcon className="h-4 w-4 mr-1" />
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                {summary.overview}
              </p>
            )}
          </div>

          {/* Key Points */}
          {keyPoints.length > 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Key Discussion Points
              </h2>
              <ul className="space-y-2">
                {keyPoints.map((point, index) => (
                  <li key={index} className="flex items-start">
                    <span className="flex-shrink-0 w-6 h-6 bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                      {index + 1}
                    </span>
                    <span className="text-gray-700 dark:text-gray-300">{point}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Speaker Transcript */}
          {video.speaker_transcript && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Speaker Transcript
              </h2>
              <SpeakerTranscriptViewer 
                transcript={video.speaker_transcript} 
                speakers={video.speakers}
              />
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Action Items */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Action Items
              </h2>
              <button
                onClick={() => setShowAddAction(true)}
                className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
              >
                <PlusIcon className="h-5 w-5" />
              </button>
            </div>

            {showAddAction && (
              <div className="mb-4 space-y-2">
                <textarea
                  value={newActionItem}
                  onChange={(e) => setNewActionItem(e.target.value)}
                  placeholder="Enter action item description..."
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  rows={2}
                />
                <div className="flex space-x-2">
                  <button
                    onClick={handleAddActionItem}
                    disabled={createActionMutation.isPending || !newActionItem.trim()}
                    className="px-3 py-1 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 disabled:opacity-50"
                  >
                    Add
                  </button>
                  <button
                    onClick={() => {
                      setShowAddAction(false);
                      setNewActionItem('');
                    }}
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}

            <div className="space-y-3">
              {actionsLoading ? (
                <LoadingSpinner size="sm" />
              ) : actionItems.length > 0 ? (
                actionItems.map((item) => (
                  <div key={item.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm text-gray-900 dark:text-white mb-1">
                          {item.description}
                        </p>
                        <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                          <span className={`px-2 py-1 rounded-full ${
                            item.priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                            item.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          }`}>
                            {item.priority}
                          </span>
                          <span className={`px-2 py-1 rounded-full ${
                            item.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                            item.status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                            'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                          }`}>
                            {item.status.replace('_', ' ')}
                          </span>
                        </div>
                      </div>
                      <button
                        onClick={() => deleteActionMutation.mutate(item.id)}
                        className="ml-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
                  No action items yet
                </p>
              )}
            </div>
          </div>

          {/* Tags */}
          {video.tags && video.tags.length > 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <TagIcon className="h-5 w-5 mr-2" />
                Tags
              </h2>
              <div className="flex flex-wrap gap-2">
                {video.tags.map((tag) => (
                  <span
                    key={tag.id}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    style={{
                      backgroundColor: `${tag.color}20`,
                      color: tag.color,
                      borderColor: tag.color,
                      borderWidth: '1px'
                    }}
                  >
                    {tag.name}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SummaryDetailPage;
