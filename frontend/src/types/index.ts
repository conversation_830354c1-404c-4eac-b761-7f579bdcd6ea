export interface Video {
  id: number;
  filename: string;
  original_filename: string;
  title: string;
  file_path: string;
  file_size: number;
  duration?: number;
  width?: number;
  height?: number;
  fps?: number;
  thumbnail_path?: string;
  transcript?: string;
  transcript_language?: string;
  upload_date: string;
  processed: boolean;
  processing_status: string;
  processing_progress?: number;
  source_url?: string;
  download_status?: string;
  download_error?: string;
  download_progress?: number;
  // Summarization fields
  is_meeting?: boolean;
  speaker_transcript?: string;
  summary_status?: string;
  summary_progress?: number;
  tags: Tag[];
  meeting_summary?: MeetingSummary;
  speakers?: Speaker[];
}

export interface Tag {
  id: number;
  name: string;
  color: string;
  description?: string;
  created_date: string;
  usage_count: number;
}

export interface VideoFilter {
  tags?: string[];
  search?: string;
  language?: string;
  processed?: boolean;
  date_from?: string;
  date_to?: string;
  category?: string;
}

export interface CategoryResponse {
  categories: string[];
  total: number;
}

export interface AnalyticsData {
  total_videos: number;
  total_tags: number;
  total_duration: number;
  processed_videos: number;
  pending_videos: number;
  top_tags: Array<{
    id: number;
    name: string;
    color: string;
    usage_count: number;
    description?: string;
  }>;
  language_distribution: Record<string, number>;
  upload_timeline: Array<{
    date: string;
    count: number;
  }>;
  duration_distribution: Record<string, number>;
}

export interface UploadResponse {
  message: string;
  uploaded_files: string[];
  failed_files: Array<{
    filename: string;
    error: string;
  }>;
  total_uploaded: number;
}

export interface ProcessingJob {
  id: number;
  video_id: number;
  job_type: string;
  status: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
}

export interface TagCloudData {
  id: number;
  name: string;
  color: string;
  description?: string;
  usage_count: number;
  size: number;
}

export interface ExportOptions {
  format: 'csv' | 'json';
  include_transcript: boolean;
  include_tags: boolean;
  tag_filter?: string[];
}

export interface ApiError {
  detail: string;
  error_code?: string;
}

export interface PaginationParams {
  skip: number;
  limit: number;
}

export interface VideoUpdateData {
  title?: string;
}

export interface TagCreateData {
  name: string;
  color: string;
  description?: string;
}

export interface TagUpdateData {
  name?: string;
  color?: string;
  description?: string;
}

export interface DownloadRequest {
  url: string;
  quality?: string;
  format?: string;
}

export interface DownloadResponse {
  message: string;
  video_id: number;
  download_status: string;
  url: string;
}

// Summarization types
export interface Speaker {
  id: number;
  video_id: number;
  speaker_label: string;
  display_name?: string;
  total_speaking_time?: number;
  created_date: string;
}

export interface ActionItem {
  id: number;
  meeting_summary_id: number;
  assigned_speaker_id?: number;
  description: string;
  priority: string;
  status: string;
  due_date?: string;
  created_date: string;
  updated_date?: string;
  assigned_speaker?: Speaker;
}

export interface MeetingSummary {
  id: number;
  video_id: number;
  overview: string;
  key_points?: string;
  created_date: string;
  updated_date?: string;
  action_items: ActionItem[];
}

export interface SummarizationRequest {
  video_id: number;
  enable_speaker_diarization?: boolean;
}

export interface SummarizationResponse {
  message: string;
  video_id: number;
  job_id: number;
  estimated_completion_time?: string;
}

export interface SummarizationStatus {
  video_id: number;
  status: string;
  progress: number;
  error_message?: string;
  estimated_completion_time?: string;
}

// Re-export VideoResponse as alias for Video for consistency with backend
export type VideoResponse = Video;
