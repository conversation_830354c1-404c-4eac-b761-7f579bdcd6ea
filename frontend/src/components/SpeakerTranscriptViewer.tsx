import React, { useState, useMemo } from 'react';
import { 
  DocumentDuplicateIcon, 
  MagnifyingGlassIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  UserGroupIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { Speaker } from '../types';

interface SpeakerTranscriptViewerProps {
  transcript: string;
  speakers?: Speaker[];
  className?: string;
}

interface SpeakerSegment {
  speaker: string;
  text: string;
  color: string;
}

const SpeakerTranscriptViewer: React.FC<SpeakerTranscriptViewerProps> = ({ 
  transcript, 
  speakers = [],
  className = '' 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Define colors for speakers
  const speakerColors = [
    '#3B82F6', // Blue
    '#EF4444', // Red
    '#10B981', // Green
    '#F59E0B', // Yellow
    '#8B5CF6', // Purple
    '#F97316', // Orange
    '#06B6D4', // Cyan
    '#84CC16', // Lime
  ];

  // Parse speaker transcript into segments
  const speakerSegments = useMemo(() => {
    const segments: SpeakerSegment[] = [];
    const lines = transcript.split('\n').filter(line => line.trim());
    
    let speakerColorMap: Record<string, string> = {};
    let colorIndex = 0;

    for (const line of lines) {
      const match = line.match(/^(Speaker \d+):\s*(.*)$/);
      if (match) {
        const [, speakerLabel, text] = match;
        
        // Assign color to speaker if not already assigned
        if (!speakerColorMap[speakerLabel]) {
          speakerColorMap[speakerLabel] = speakerColors[colorIndex % speakerColors.length];
          colorIndex++;
        }

        segments.push({
          speaker: speakerLabel,
          text: text.trim(),
          color: speakerColorMap[speakerLabel]
        });
      }
    }

    return segments;
  }, [transcript]);

  // Get speaker display name
  const getSpeakerDisplayName = (speakerLabel: string) => {
    const speaker = speakers.find(s => s.speaker_label === speakerLabel);
    return speaker?.display_name || speakerLabel;
  };

  // Get speaker stats
  const getSpeakerStats = (speakerLabel: string) => {
    const speaker = speakers.find(s => s.speaker_label === speakerLabel);
    if (!speaker?.total_speaking_time) return null;
    
    const minutes = Math.floor(speaker.total_speaking_time / 60);
    const seconds = Math.floor(speaker.total_speaking_time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleCopyTranscript = async () => {
    try {
      await navigator.clipboard.writeText(transcript);
      toast.success('Speaker transcript copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy transcript');
    }
  };

  const highlightSearchTerm = (text: string, term: string): React.ReactNode => {
    if (!term.trim()) return text;

    const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-600 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    );
  };

  const filteredSegments = useMemo(() => {
    if (!searchTerm.trim()) return speakerSegments;
    
    const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
    return speakerSegments.filter(segment => 
      regex.test(segment.text) || regex.test(segment.speaker)
    );
  }, [speakerSegments, searchTerm]);

  const displaySegments = isExpanded ? filteredSegments : filteredSegments.slice(0, 5);

  const searchMatches = searchTerm.trim() 
    ? filteredSegments.length
    : 0;

  // Get unique speakers for stats
  const uniqueSpeakers = useMemo(() => {
    const speakerMap = new Map();
    speakerSegments.forEach(segment => {
      if (!speakerMap.has(segment.speaker)) {
        speakerMap.set(segment.speaker, {
          label: segment.speaker,
          color: segment.color,
          segmentCount: 0
        });
      }
      speakerMap.get(segment.speaker).segmentCount++;
    });
    return Array.from(speakerMap.values());
  }, [speakerSegments]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with Speaker Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <UserGroupIcon className="h-5 w-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {uniqueSpeakers.length} Speaker{uniqueSpeakers.length !== 1 ? 's' : ''}
          </span>
        </div>
        <div className="flex items-center space-x-4">
          {uniqueSpeakers.map((speaker) => (
            <div key={speaker.label} className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: speaker.color }}
              />
              <span className="text-xs text-gray-600 dark:text-gray-400">
                {getSpeakerDisplayName(speaker.label)}
                {getSpeakerStats(speaker.label) && (
                  <span className="ml-1">({getSpeakerStats(speaker.label)})</span>
                )}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Search and Actions */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search in speaker transcript..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
          />
        </div>
        <button
          onClick={handleCopyTranscript}
          className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          title="Copy speaker transcript"
        >
          <DocumentDuplicateIcon className="h-4 w-4" />
        </button>
      </div>

      {/* Search Results Info */}
      {searchTerm.trim() && (
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {searchMatches > 0 ? (
            `Found ${searchMatches} segment${searchMatches !== 1 ? 's' : ''} matching "${searchTerm}"`
          ) : (
            'No matches found'
          )}
        </div>
      )}

      {/* Speaker Transcript Content */}
      <div className="space-y-3">
        {displaySegments.map((segment, index) => (
          <div key={index} className="flex space-x-3">
            {/* Speaker Avatar */}
            <div className="flex-shrink-0">
              <div 
                className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium"
                style={{ backgroundColor: segment.color }}
              >
                {segment.speaker.replace('Speaker ', '')}
              </div>
            </div>
            
            {/* Speaker Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <span 
                  className="text-sm font-medium"
                  style={{ color: segment.color }}
                >
                  {getSpeakerDisplayName(segment.speaker)}
                </span>
                {getSpeakerStats(segment.speaker) && (
                  <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                    <ClockIcon className="h-3 w-3 mr-1" />
                    {getSpeakerStats(segment.speaker)}
                  </span>
                )}
              </div>
              <div className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                {highlightSearchTerm(segment.text, searchTerm)}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Expand/Collapse Button */}
      {filteredSegments.length > 5 && (
        <div className="text-center">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
          >
            {isExpanded ? (
              <>
                <ChevronUpIcon className="h-4 w-4 mr-1" />
                Show Less
              </>
            ) : (
              <>
                <ChevronDownIcon className="h-4 w-4 mr-1" />
                Show {filteredSegments.length - 5} More Segments
              </>
            )}
          </button>
        </div>
      )}

      {/* Transcript Stats */}
      <div className="text-xs text-gray-500 dark:text-gray-400 border-t border-gray-100 dark:border-gray-700 pt-2">
        {speakerSegments.length} segments • {transcript.split(/\s+/).length} words • {transcript.length} characters
      </div>
    </div>
  );
};

export default SpeakerTranscriptViewer;
