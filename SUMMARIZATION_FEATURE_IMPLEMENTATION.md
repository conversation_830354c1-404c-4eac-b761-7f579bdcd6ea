# Video Summarization Feature Implementation

## Overview

This document outlines the comprehensive video summarization feature that has been implemented for the tagTok project. The feature includes speaker diarization, AI-powered meeting summaries, action item extraction, and a complete management interface.

## Features Implemented

### 1. Database Models
- **Extended Video Model**: Added summarization fields (`is_meeting`, `speaker_transcript`, `summary_status`, `summary_progress`)
- **MeetingSummary Model**: Stores AI-generated meeting overviews and key points
- **Speaker Model**: Tracks identified speakers with labels, display names, and speaking time
- **ActionItem Model**: Manages action items with assignments, priorities, and status tracking

### 2. Backend API (FastAPI)
- **New Router**: `/summarization` endpoints for all summarization operations
- **SummarizationService**: Core business logic for processing meeting videos
- **Enhanced AI Utils**: 
  - `SpeakerDiarizer`: Identifies different speakers in recordings
  - `MeetingSummarizer`: Generates comprehensive meeting summaries using Ollama
- **Processing Pipeline**: Automated workflow from upload to summary generation

### 3. Frontend Interface (React)
- **New Navigation Section**: "Summarize" with DocumentTextIcon
- **Upload Interface**: Dedicated meeting video upload page with progress tracking
- **Summary Management**: Comprehensive interface for viewing and managing summaries
- **API Integration**: Complete client-side API functions for all summarization endpoints

## Technical Architecture

### Backend Components

#### Database Schema
```sql
-- Extended videos table
ALTER TABLE videos ADD COLUMN is_meeting BOOLEAN DEFAULT FALSE;
ALTER TABLE videos ADD COLUMN speaker_transcript TEXT;
ALTER TABLE videos ADD COLUMN summary_status VARCHAR DEFAULT 'none';
ALTER TABLE videos ADD COLUMN summary_progress INTEGER DEFAULT 0;

-- New tables
CREATE TABLE meeting_summaries (
    id SERIAL PRIMARY KEY,
    video_id INTEGER UNIQUE REFERENCES videos(id),
    overview TEXT NOT NULL,
    key_points TEXT,
    created_date TIMESTAMP DEFAULT NOW(),
    updated_date TIMESTAMP
);

CREATE TABLE speakers (
    id SERIAL PRIMARY KEY,
    video_id INTEGER REFERENCES videos(id),
    speaker_label VARCHAR NOT NULL,
    display_name VARCHAR,
    total_speaking_time FLOAT,
    created_date TIMESTAMP DEFAULT NOW()
);

CREATE TABLE action_items (
    id SERIAL PRIMARY KEY,
    meeting_summary_id INTEGER REFERENCES meeting_summaries(id),
    assigned_speaker_id INTEGER REFERENCES speakers(id),
    description TEXT NOT NULL,
    priority VARCHAR DEFAULT 'medium',
    status VARCHAR DEFAULT 'pending',
    due_date TIMESTAMP,
    created_date TIMESTAMP DEFAULT NOW(),
    updated_date TIMESTAMP
);
```

#### API Endpoints
```
POST /summarization/upload - Upload meeting videos
GET /summarization/videos - List meeting videos
GET /summarization/videos/{id} - Get meeting video details
POST /summarization/videos/{id}/summarize - Start summarization
GET /summarization/videos/{id}/status - Get summarization status
GET /summarization/videos/{id}/summary - Get meeting summary
PUT /summarization/videos/{id}/summary - Update meeting summary
GET /summarization/videos/{id}/speakers - Get speakers
PUT /summarization/speakers/{id} - Update speaker info
GET /summarization/videos/{id}/action-items - Get action items
POST /summarization/videos/{id}/action-items - Create action item
PUT /summarization/action-items/{id} - Update action item
DELETE /summarization/action-items/{id} - Delete action item
DELETE /summarization/videos/{id} - Delete meeting video
```

#### Processing Pipeline
1. **Video Upload**: Meeting videos uploaded with `is_meeting=true`
2. **Transcription**: Standard Whisper transcription
3. **Speaker Diarization**: Identify different speakers using pyannote.audio (with fallback)
4. **Speaker Transcript**: Format transcript with speaker labels
5. **AI Summarization**: Generate summary using Ollama LLM
6. **Action Item Extraction**: Extract and assign action items
7. **Tag Generation**: Auto-generate relevant tags

### Frontend Components

#### Navigation
- Added "Summarize" section to main navigation
- Routes: `/summarize` and `/summarize/upload`

#### Pages
- **SummarizePage**: Main dashboard showing all meeting videos with status
- **SummarizeUploadPage**: Dedicated upload interface for meeting videos

#### API Integration
- **summarizationApi**: Complete client-side API functions
- **Types**: TypeScript interfaces for all summarization models
- **Error Handling**: Comprehensive error handling and loading states

## AI Integration

### Speaker Diarization
- **Primary**: pyannote.audio pipeline for production-quality speaker identification
- **Fallback**: Mock speaker assignment for development/testing
- **Output**: Speaker-labeled transcript format

### Meeting Summarization (Ollama)
- **Model**: llama3.2:3b for optimal performance/quality balance
- **Prompt Engineering**: Structured prompts for consistent output
- **Output Format**: JSON with overview, key points, action items, and tags
- **Fallback**: Rule-based summary generation

### Features Generated
- **Overview**: 2-3 sentence meeting summary
- **Key Points**: List of main discussion topics
- **Action Items**: Specific tasks with speaker assignments and priorities
- **Tags**: Relevant tags for searchability

## Usage Workflow

### For Users
1. **Upload**: Navigate to Summarize → Upload and select meeting videos
2. **Processing**: Automatic transcription, speaker identification, and summarization
3. **Review**: View generated summaries, edit speaker names, manage action items
4. **Organization**: Use auto-generated tags for easy searching and filtering

### For Developers
1. **Backend**: All summarization logic in `SummarizationService`
2. **AI Processing**: Extend `SpeakerDiarizer` and `MeetingSummarizer` classes
3. **Frontend**: Use `summarizationApi` for all backend interactions
4. **Customization**: Modify prompts in `MeetingSummarizer` for different output formats

## Configuration

### Environment Variables
```bash
# Ollama Configuration (optional - will fallback if not available)
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=llama3.2:3b

# Speaker Diarization (optional - will fallback to mock)
PYANNOTE_AUTH_TOKEN=your_huggingface_token
```

### Dependencies
```bash
# Backend (optional for full functionality)
pip install pyannote.audio torch
pip install requests  # for Ollama integration

# Frontend (already included)
# All required dependencies are already in package.json
```

## File Structure

### Backend Files Added/Modified
```
backend/
├── models/
│   ├── database.py (extended Video model, added new models)
│   └── schemas.py (added summarization schemas)
├── routers/
│   └── summarization.py (new router)
├── services/
│   └── summarization_service.py (new service)
├── utils/
│   └── ai_utils.py (added SpeakerDiarizer, MeetingSummarizer)
└── main.py (added summarization router)
```

### Frontend Files Added/Modified
```
frontend/src/
├── components/
│   └── Layout.tsx (added Summarize navigation)
├── pages/
│   ├── SummarizePage.tsx (new)
│   └── SummarizeUploadPage.tsx (new)
├── types/
│   └── index.ts (added summarization types)
├── utils/
│   └── api.ts (added summarizationApi)
└── App.tsx (added summarization routes)
```

## Testing

### Manual Testing Steps
1. **Upload Test**: Upload a meeting video through `/summarize/upload`
2. **Processing Test**: Verify automatic processing pipeline
3. **Summary Test**: Check generated summary quality and format
4. **Speaker Test**: Verify speaker identification and labeling
5. **Action Items Test**: Test action item creation and management
6. **Integration Test**: Verify end-to-end workflow

### Automated Testing
- Backend: Unit tests for `SummarizationService` methods
- Frontend: Component tests for upload and summary interfaces
- Integration: API endpoint testing with mock data

## Performance Considerations

### Optimization
- **Async Processing**: All AI operations run in background tasks
- **Progress Tracking**: Real-time progress updates for users
- **Caching**: Ollama responses cached to avoid reprocessing
- **Fallbacks**: Graceful degradation when AI services unavailable

### Scalability
- **Queue System**: Ready for Redis/Celery integration for production
- **Model Management**: Configurable AI models and endpoints
- **Resource Management**: CPU/GPU usage monitoring for AI operations

## Future Enhancements

### Planned Features
1. **Custom Speaker Names**: Allow users to set custom speaker names
2. **Meeting Templates**: Pre-defined meeting types with custom prompts
3. **Integration**: Calendar integration for automatic meeting detection
4. **Analytics**: Meeting analytics and insights dashboard
5. **Export**: Export summaries to various formats (PDF, Word, etc.)

### Technical Improvements
1. **Real-time Processing**: WebSocket updates for processing status
2. **Advanced Diarization**: Multiple speaker identification models
3. **Custom Models**: Fine-tuned models for specific meeting types
4. **Multi-language**: Support for multiple languages in summarization

## Conclusion

The video summarization feature provides a comprehensive solution for meeting video analysis with:
- Automated speaker identification
- AI-powered summary generation
- Action item extraction and management
- Intuitive user interface
- Robust error handling and fallbacks

The implementation follows the existing tagTok architecture patterns and provides a solid foundation for future enhancements.
