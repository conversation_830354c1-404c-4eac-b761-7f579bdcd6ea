const { chromium } = require('playwright');

async function debugAuth() {
  console.log('🔍 Debugging authentication...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Test 1: Check localStorage after registration
    console.log('\n📋 Test 1: Checking localStorage after registration...');
    await page.goto('http://localhost:3000/register');
    await page.waitForTimeout(1000);
    
    const testEmail = `debug${Date.now()}@example.com`;
    const testUsername = `debug${Date.now()}`;
    const testPassword = 'testpassword123';
    
    await page.fill('input[name="email"], input[id="email"]', testEmail);
    await page.fill('input[name="username"], input[id="username"]', testUsername);
    await page.fill('input[name="password"], input[id="password"]', testPassword);
    await page.fill('input[name="confirmPassword"], input[id="confirmPassword"]', testPassword);
    
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    // Check localStorage
    const authData = await page.evaluate(() => {
      return {
        token: localStorage.getItem('auth_token'),
        user: localStorage.getItem('auth_user'),
        allKeys: Object.keys(localStorage)
      };
    });
    
    console.log('📦 LocalStorage data:');
    console.log(`Token: ${authData.token ? '✅ Present' : '❌ Missing'}`);
    console.log(`User: ${authData.user ? '✅ Present' : '❌ Missing'}`);
    console.log(`All keys: ${authData.allKeys.join(', ')}`);
    
    if (authData.token) {
      console.log(`Token preview: ${authData.token.substring(0, 50)}...`);
    }
    
    if (authData.user) {
      try {
        const userData = JSON.parse(authData.user);
        console.log(`User data: ${userData.username} (${userData.email})`);
      } catch (e) {
        console.log('❌ User data is not valid JSON');
      }
    }
    
    // Test 2: Check if auth context is working
    console.log('\n📋 Test 2: Checking auth context...');
    
    const authContextData = await page.evaluate(() => {
      // Try to access React DevTools or check for auth context
      return {
        currentUrl: window.location.href,
        title: document.title,
        hasAuthProvider: !!window.React,
        bodyText: document.body.innerText.substring(0, 200)
      };
    });
    
    console.log(`Current URL: ${authContextData.currentUrl}`);
    console.log(`Page title: ${authContextData.title}`);
    console.log(`Body preview: ${authContextData.bodyText}...`);
    
    // Test 3: Manual API call with token
    console.log('\n📋 Test 3: Manual API call with token...');
    
    if (authData.token) {
      const apiResponse = await page.evaluate(async (token) => {
        try {
          const response = await fetch('http://localhost:8791/api/auth/me', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          return {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            body: response.ok ? await response.json() : await response.text()
          };
        } catch (error) {
          return {
            error: error.message
          };
        }
      }, authData.token);
      
      console.log('🌐 API Response:');
      console.log(`Status: ${apiResponse.status} ${apiResponse.statusText}`);
      if (apiResponse.error) {
        console.log(`Error: ${apiResponse.error}`);
      } else {
        console.log(`Body:`, apiResponse.body);
      }
    }
    
    // Test 4: Check network requests
    console.log('\n📋 Test 4: Monitoring network requests...');
    
    const requests = [];
    page.on('request', request => {
      if (request.url().includes('localhost:8791')) {
        requests.push({
          url: request.url(),
          method: request.method(),
          headers: request.headers()
        });
      }
    });
    
    // Trigger a page reload to see requests
    await page.reload();
    await page.waitForTimeout(3000);
    
    console.log('📡 Network requests:');
    requests.forEach((req, index) => {
      console.log(`${index + 1}. ${req.method} ${req.url}`);
      if (req.headers.authorization) {
        console.log(`   Auth: ${req.headers.authorization.substring(0, 30)}...`);
      } else {
        console.log(`   Auth: ❌ Missing`);
      }
    });

    console.log('\n🎯 Debug completed!');

  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the debug
debugAuth().catch(console.error);
